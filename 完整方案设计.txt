# AI Studio 完整方案设计

## 1. 项目概述

### 1.1 项目背景

AI Studio 是一个基于 Vue3 + TypeScript + Vite + Tauri 2.x 技术栈的中文AI助手桌面应用，专为 Windows/macOS 平台设计。项目旨在提供本地大模型部署、知识库管理、局域网共享等企业级功能，实现完全离线的AI助手解决方案。

### 1.2 项目目标

- **本地化部署**：支持本地大模型推理，无需依赖云端服务
- **知识库管理**：提供文档解析、向量搜索、RAG增强等功能
- **局域网协作**：实现设备间模型、知识库、配置的共享
- **多模态支持**：集成OCR、TTS、语音识别等多媒体处理能力
- **企业级质量**：提供生产环境可用的稳定性和性能
- **插件生态**：支持第三方插件扩展和云端模型API集成

### 1.3 核心功能特性

1. **智能对话系统**
   - 支持流式对话、多模态输入、会话管理
   - Markdown渲染、代码高亮、数学公式支持
   - 多会话并行处理、会话历史持久化

2. **知识库管理系统**
   - 支持PDF、Word、TXT等多种格式文档上传
   - 基于ChromaDB实现向量检索和RAG增强
   - 智能文档分块、语义搜索、知识图谱构建

3. **模型管理系统**
   - 本地模型加载卸载、在线模型下载
   - 性能监控、模型量化、GPU加速支持
   - HuggingFace集成、断点续传下载

4. **多模态处理系统**
   - 图像识别、语音识别(STT)、语音合成(TTS)
   - OCR文字识别、视频分析、格式转换
   - 批量处理、任务队列管理

5. **远程配置系统**
   - API密钥管理、远程模型配置、代理设置
   - 支持OpenAI、Anthropic、Google等主流AI服务商
   - 配置同步、安全存储、连通性验证

6. **局域网共享系统**
   - mDNS设备发现、P2P文件传输、分布式推理
   - 资源共享、权限管理、安全认证
   - 跨设备协同、实时同步

7. **插件扩展系统**
   - WASM插件运行环境、插件市场
   - 沙箱隔离、权限管理、热插拔支持
   - 自定义API集成、JavaScript脚本支持

8. **系统管理功能**
   - 主题切换、语言切换、用户信息管理
   - 性能监控、日志管理、自动更新
   - 数据备份、健康检查、故障诊断

## 2. 技术架构

### 2.1 整体架构设计

AI Studio 采用现代化的桌面应用架构，基于 Tauri 框架实现跨平台支持：

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
│  Vue3 + TypeScript + Vite + Naive UI + Tailwind CSS       │
├─────────────────────────────────────────────────────────────┤
│                    应用层 (Application)                     │
│              Tauri 2.x + Rust Backend                     │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (Services)                       │
│  AI推理引擎 │ 向量数据库 │ 文件处理 │ 网络通信 │ 插件系统    │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data)                           │
│     SQLite 主数据库 │ ChromaDB 向量库 │ 本地文件存储       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 前端技术栈

**核心框架**
- **Vue 3.4+**：采用 Composition API，提供响应式数据管理
- **TypeScript 5.0+**：强类型支持，提升代码质量和开发效率
- **Vite 7.0+**：快速构建工具，支持热更新和模块化开发

**UI 组件库**
- **Naive UI 2.0+**：现代化 Vue3 组件库，提供丰富的UI组件
- **Tailwind CSS 3.0+**：原子化CSS框架，快速样式开发
- **SCSS**：CSS预处理器，支持变量和混入

**状态管理**
- **Pinia 2.0+**：Vue3 官方推荐的状态管理库
- **Vue Router 4.0+**：单页应用路由管理

**工具库**
- **Vue I18n 9.0+**：国际化支持
- **Iconify**：图标库，提供丰富的图标资源
- **@vueuse/markdown**：Markdown渲染支持
- **Prism.js**：代码语法高亮
- **ECharts 5.0+**：数据可视化图表库

### 2.3 后端技术栈

**核心框架**
- **Tauri 2.x**：跨平台桌面应用框架
- **Rust 1.75+**：系统级编程语言，提供高性能和内存安全

**数据存储**
- **SQLite 3.45+**：轻量级关系型数据库，用于主要数据存储
- **ChromaDB**：向量数据库，用于知识库向量存储和检索

**AI 推理引擎**
- **Candle-core**：Rust原生AI推理框架
- **llama.cpp**：高性能大语言模型推理引擎

**网络通信**
- **Tokio**：异步运行时，支持高并发网络操作
- **Reqwest**：HTTP客户端库
- **mdns**：多播DNS服务发现
- **tokio-tungstenite**：WebSocket支持

**文档处理**
- **pdf-extract**：PDF文档解析
- **docx-rs**：Word文档处理
- **calamine**：Excel文档处理

**多媒体处理**
- **image + imageproc**：图像处理和分析
- **rodio**：音频播放和处理
- **whisper-rs**：语音识别引擎

**工具库**
- **Serde + serde_json**：序列化和反序列化
- **thiserror + anyhow**：错误处理
- **tracing + tracing-subscriber**：日志系统
- **aes-gcm + ring**：加密和安全

### 2.4 开发工具链

**包管理**
- **npm/pnpm**：前端包管理器
- **Cargo**：Rust包管理器

**代码质量**
- **Prettier**：前端代码格式化
- **ESLint**：前端代码检查
- **rustfmt**：Rust代码格式化
- **Clippy**：Rust代码检查

**测试框架**
- **Vitest**：前端单元测试
- **cargo test**：Rust单元测试

**构建工具**
- **Tauri CLI**：应用构建和打包
- **Git + Git LFS**：版本控制（支持大文件）

## 3. 系统设计

### 3.1 数据库设计

**主数据库 (SQLite)**

系统采用 SQLite 作为主数据库，存储应用的核心业务数据：

```sql
-- 会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    model_id TEXT,
    system_prompt TEXT,
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2048,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY,
    session_id TEXT REFERENCES chat_sessions(id),
    role TEXT NOT NULL, -- 'user' | 'assistant' | 'system'
    content TEXT NOT NULL,
    attachments JSON,
    tokens_used INTEGER,
    response_time REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    embedding_model TEXT DEFAULT 'sentence-transformers/all-MiniLM-L6-v2',
    chunk_size INTEGER DEFAULT 512,
    chunk_overlap INTEGER DEFAULT 50,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    kb_id TEXT REFERENCES knowledge_bases(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- 'pdf', 'docx', 'txt', 'md'
    size INTEGER,
    path TEXT NOT NULL,
    status TEXT DEFAULT 'processing', -- 'processing', 'completed', 'failed'
    chunks_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    author TEXT,
    version TEXT,
    size INTEGER,
    quantization TEXT, -- 'none', 'q4_0', 'q4_1', 'q8_0'
    architecture TEXT, -- 'llama', 'mistral', 'qwen'
    context_length INTEGER DEFAULT 2048,
    status TEXT DEFAULT 'available', -- 'available', 'downloading', 'loaded', 'error'
    local_path TEXT,
    download_url TEXT,
    huggingface_id TEXT,
    config JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**向量数据库 (ChromaDB)**

用于存储文档向量和实现语义搜索：

- **Collection 结构**：每个知识库对应一个 Collection
- **向量维度**：根据 embedding 模型确定（通常为 384 或 768 维）
- **元数据存储**：文档ID、块索引、页码等信息
- **距离计算**：使用余弦相似度进行语义匹配

### 3.2 安全设计

**数据加密**
- **敏感数据加密**：API密钥、代理密码等使用 AES-GCM 加密存储
- **传输加密**：网络通信使用 TLS 1.3 加密
- **本地存储**：支持数据库文件加密选项

**权限管理**
- **插件沙箱**：插件运行在隔离环境中，限制系统访问
- **网络权限**：细粒度控制网络访问权限
- **文件权限**：限制文件系统访问范围

**身份认证**
- **设备认证**：局域网设备间使用公钥加密认证
- **API认证**：支持多种API认证方式（API Key、OAuth等）
- **会话管理**：安全的会话状态管理

### 3.3 性能设计

**内存管理**
- **模型加载优化**：支持模型分层加载，减少内存占用
- **缓存策略**：智能缓存常用数据，提升响应速度
- **垃圾回收**：定期清理无用数据和临时文件

**并发处理**
- **异步架构**：基于 Tokio 的异步处理，支持高并发
- **任务队列**：后台任务队列处理耗时操作
- **资源池**：数据库连接池、HTTP连接池等资源复用

**性能监控**
- **实时监控**：CPU、内存、GPU使用率实时监控
- **性能指标**：推理速度、响应时间等关键指标
- **性能优化**：自动性能调优和资源分配

## 4. 功能模块设计

### 4.1 聊天模块 (Chat)

**功能描述**
提供AI对话、会话管理、流式响应等核心聊天功能，支持多模态输入和智能上下文管理。

**核心特性**
- **流式响应**：基于SSE (Server-Sent Events) 实现实时token流输出
- **会话管理**：支持多会话并行、会话历史持久化、会话导出
- **RAG集成**：自动检索相关知识库内容增强回复质量
- **多模态输入**：支持文本、图片、音频等多种输入方式
- **Markdown渲染**：支持代码高亮、数学公式、表格等富文本显示

**技术实现**
- **前端**：Vue3 + TypeScript，使用 EventSource 接收流式数据
- **后端**：Rust + Tokio，异步处理推理请求
- **数据存储**：SQLite 存储会话和消息数据
- **推理引擎**：支持本地模型（Candle/llama.cpp）和远程API

**API接口**
```typescript
// 发送消息
POST /api/chat/send
Request: {
  session_id: string;
  message: string;
  attachments?: Array<{type: string; url: string; name: string}>;
  model_config?: {model_id: string; temperature: number; max_tokens: number};
}

// 流式响应
GET /api/chat/stream/{message_id}
Response: Server-Sent Events

// 会话管理
GET /api/chat/sessions
POST /api/chat/sessions
PUT /api/chat/sessions/{id}
DELETE /api/chat/sessions/{id}
```

### 4.2 知识库模块 (Knowledge Base)

**功能描述**
提供文档管理、向量搜索、RAG检索等知识库功能，支持多种文档格式和智能语义搜索。

**核心特性**
- **文档解析**：支持PDF、Word、Excel、Markdown、TXT等多种格式
- **向量化存储**：使用ChromaDB进行向量存储和检索
- **增量索引**：支持文档变更检测和增量更新
- **语义搜索**：基于embedding模型的高质量语义搜索
- **文档切分**：智能文档分块，保持语义完整性
- **知识图谱**：构建实体关系图，增强检索效果

**技术实现**
- **文档解析**：pdf-extract、docx-rs、calamine等Rust库
- **向量化**：sentence-transformers模型生成文档向量
- **存储**：ChromaDB向量数据库 + SQLite元数据存储
- **搜索**：余弦相似度计算 + 重排序算法

**API接口**
```typescript
// 知识库管理
GET /api/knowledge
POST /api/knowledge
PUT /api/knowledge/{id}
DELETE /api/knowledge/{id}

// 文档管理
POST /api/knowledge/{id}/upload
GET /api/knowledge/{id}/documents
DELETE /api/knowledge/{kb_id}/documents/{doc_id}

// 语义搜索
POST /api/knowledge/{id}/search
Request: {
  query: string;
  limit?: number;
  threshold?: number;
  filters?: object;
}
```

### 4.3 模型管理模块 (Model Management)

**功能描述**
提供本地模型下载、加载、量化、部署等功能，支持HuggingFace生态和多种推理引擎。

**核心特性**
- **HuggingFace集成**：支持从HF Hub下载模型，包含镜像站切换
- **断点续传**：支持大文件分片下载和自动恢复
- **模型量化**：集成GPTQ、AWQ等量化工具，减少内存占用
- **GPU加速**：支持CUDA、Metal等GPU加速框架
- **一键部署**：自动化模型部署和服务管理
- **性能监控**：实时监控模型推理性能和资源使用

**技术实现**
- **下载引擎**：多线程分片下载，支持断点续传
- **模型加载**：Candle-core和llama.cpp双引擎支持
- **量化工具**：集成主流量化算法
- **监控系统**：实时性能指标收集和分析

**API接口**
```typescript
// 模型管理
GET /api/models
GET /api/models/search
POST /api/models/download
POST /api/models/upload
DELETE /api/models/{id}

// 模型操作
POST /api/models/{id}/load
POST /api/models/{id}/unload
POST /api/models/{id}/quantize

// 下载管理
GET /api/models/downloads
POST /api/models/downloads/{id}/pause
POST /api/models/downloads/{id}/resume
```

### 4.4 多模态处理模块 (Multimodal)

**功能描述**
提供OCR识别、语音处理、图像分析等多模态功能，支持多种媒体格式处理和批量任务管理。

**核心特性**
- **OCR识别**：集成Tesseract或PaddleOCR进行文字识别
- **语音处理**：支持语音转文字(ASR)和文字转语音(TTS)
- **图像分析**：集成YOLO、CLIP等模型进行图像理解
- **视频处理**：支持视频分析和字幕生成
- **格式转换**：支持多种音视频格式转换
- **批量处理**：支持批量文件处理和任务队列

**技术实现**
- **OCR引擎**：Tesseract + 自定义优化算法
- **语音引擎**：whisper-rs + rodio音频处理
- **图像处理**：image + imageproc库
- **任务队列**：异步任务调度和进度管理

**API接口**
```typescript
// 多模态处理
POST /api/multimodal/ocr
POST /api/multimodal/tts
POST /api/multimodal/asr
POST /api/multimodal/image/analyze
POST /api/multimodal/video/analyze
POST /api/multimodal/convert

// 任务管理
GET /api/multimodal/tasks
GET /api/multimodal/tasks/{id}
DELETE /api/multimodal/tasks/{id}
GET /api/multimodal/history
```

### 4.5 远程配置模块 (Remote Configuration)

**功能描述**
提供API密钥管理、远程模型配置、代理设置等功能，支持多种AI服务商和配置同步。

**核心特性**
- **API密钥管理**：支持OpenAI、Anthropic、Google等主流AI服务商
- **代理配置**：支持HTTP/HTTPS/SOCKS5代理设置
- **配置同步**：支持配置云端备份和多设备同步
- **安全存储**：敏感信息加密存储，支持主密码保护
- **配置模板**：预设常用配置模板，快速配置
- **配置验证**：自动验证API密钥有效性和网络连通性

**技术实现**
- **加密存储**：AES-GCM加密敏感配置信息
- **代理支持**：reqwest + proxy配置
- **配置验证**：异步连通性测试
- **模板系统**：预定义配置模板

**API接口**
```typescript
// 配置管理
GET /api/remote/configs
POST /api/remote/configs
PUT /api/remote/configs/{id}
DELETE /api/remote/configs/{id}
POST /api/remote/configs/{id}/test
POST /api/remote/configs/{id}/activate

// 模板管理
GET /api/remote/templates
POST /api/remote/templates

// 配置同步
POST /api/remote/sync/backup
POST /api/remote/sync/restore
```

### 4.6 局域网共享模块 (Network Sharing)

**功能描述**
提供局域网设备发现、P2P通信、资源共享等功能，实现设备间的协同工作。

**核心特性**
- **mDNS发现**：基于mDNS协议的局域网设备自动发现
- **P2P通信**：WebRTC或自定义协议的点对点通信
- **文件传输**：支持大文件分片传输和断点续传
- **权限管理**：细粒度的访问控制和安全认证
- **资源共享**：模型、知识库、配置的跨设备共享
- **分布式推理**：多设备协同推理，提升性能

**技术实现**
- **设备发现**：mdns库实现服务发现
- **P2P通信**：自定义协议 + TLS加密
- **文件传输**：分片传输 + 校验和验证
- **权限控制**：基于公钥的身份认证

**API接口**
```typescript
// 设备管理
GET /api/network/discover
POST /api/network/connect
POST /api/network/disconnect
GET /api/network/nodes

// 资源共享
POST /api/network/share
GET /api/network/resources

// 传输管理
POST /api/network/transfer
GET /api/network/transfers
POST /api/network/transfers/{id}/pause
POST /api/network/transfers/{id}/resume
```

### 4.7 插件系统模块 (Plugin System)

**功能描述**
支持第三方插件扩展、插件市场、云端API集成等功能，提供安全的插件运行环境。

**核心特性**
- **WASM插件**：基于WebAssembly的安全插件运行环境
- **插件市场**：在线插件商店，支持搜索、安装、更新
- **API集成**：支持自定义API接口和JavaScript脚本
- **沙箱隔离**：插件运行在隔离环境中，确保系统安全
- **权限管理**：细粒度的插件权限控制
- **热插拔**：支持插件的动态加载和卸载

**技术实现**
- **WASM运行时**：wasmtime + 安全沙箱
- **权限系统**：基于能力的权限模型
- **插件API**：标准化插件接口定义
- **市场服务**：插件发现和分发服务

**API接口**
```typescript
// 插件管理
GET /api/plugins
POST /api/plugins/install
DELETE /api/plugins/{id}
POST /api/plugins/{id}/enable
POST /api/plugins/{id}/disable

// 插件配置
GET /api/plugins/{id}/config
PUT /api/plugins/{id}/config

// 插件执行
POST /api/plugins/{id}/execute

// 插件市场
GET /api/plugins/market
GET /api/plugins/market/search
POST /api/plugins/market/{id}/install
```

### 4.8 系统管理模块 (System Management)

**功能描述**
提供系统监控、配置管理、日志管理等系统功能，确保应用稳定运行。

**核心特性**
- **性能监控**：实时监控CPU、内存、GPU使用情况
- **配置管理**：统一的配置文件管理和热更新
- **日志系统**：结构化日志记录和查询
- **自动更新**：应用自动更新和版本管理
- **数据备份**：自动数据备份和恢复机制
- **系统诊断**：健康检查和故障诊断

**技术实现**
- **监控系统**：tracing + 自定义指标收集
- **配置热更新**：文件监听 + 动态重载
- **备份系统**：增量备份 + 压缩存储
- **更新机制**：差分更新 + 签名验证

**API接口**
```typescript
// 系统信息
GET /api/system/info
GET /api/system/performance
GET /api/system/health

// 配置管理
GET /api/system/configs
PUT /api/system/configs

// 日志管理
GET /api/system/logs

// 备份管理
POST /api/system/backup
GET /api/system/backups
POST /api/system/restore

// 更新管理
POST /api/system/update/check
POST /api/system/update/install
```

## 5. 项目结构

### 5.1 前端目录结构 (src/)

```
src/
├── api/                          # API服务层 - 封装所有后台接口调用
│   ├── base.ts                   # 基础API配置和拦截器
│   ├── chat.ts                   # 聊天相关API接口
│   ├── knowledge.ts              # 知识库相关API接口
│   ├── model.ts                  # 模型管理相关API接口
│   ├── multimodal.ts             # 多模态处理相关API接口
│   ├── network.ts                # 网络共享相关API接口
│   ├── remote.ts                 # 远程配置相关API接口
│   ├── system.ts                 # 系统管理相关API接口
│   ├── plugin.ts                 # 插件系统相关API接口
│   └── index.ts                  # API模块统一导出
├── assets/                       # 静态资源文件
│   ├── images/                   # 图片资源
│   │   ├── icons/                # 图标文件
│   │   ├── logos/                # Logo文件
│   │   └── backgrounds/          # 背景图片
│   ├── fonts/                    # 字体文件
│   └── styles/                   # 全局样式文件
│       ├── variables.scss        # SCSS变量定义
│       ├── mixins.scss           # SCSS混入函数
│       ├── reset.scss            # 样式重置
│       ├── themes/               # 主题样式
│       │   ├── light.scss        # 浅色主题
│       │   └── dark.scss         # 深色主题
│       └── global.scss           # 全局样式
├── components/                   # 可复用组件 - 按功能模块组织
│   ├── common/                   # 通用组件
│   │   ├── AppHeader.vue         # 应用头部导航组件
│   │   ├── AppSidebar.vue        # 应用侧边栏组件
│   │   ├── AppFooter.vue         # 应用底部组件
│   │   ├── LoadingSpinner.vue    # 加载动画组件
│   │   ├── ErrorBoundary.vue     # 错误边界组件
│   │   ├── ConfirmDialog.vue     # 确认对话框组件
│   │   ├── FileUpload.vue        # 文件上传组件
│   │   ├── ProgressBar.vue       # 进度条组件
│   │   ├── SearchInput.vue       # 搜索输入组件
│   │   ├── ThemeToggle.vue       # 主题切换组件
│   │   ├── LanguageSwitch.vue    # 语言切换组件
│   │   └── UserDropdown.vue      # 用户下拉菜单组件
│   ├── chat/                     # 聊天模块组件
│   │   ├── ChatContainer.vue     # 聊天容器组件
│   │   ├── ChatSidebar.vue       # 聊天侧边栏组件
│   │   ├── ChatInput.vue         # 聊天输入组件
│   │   ├── ChatMessage.vue       # 聊天消息组件
│   │   ├── MessageList.vue       # 消息列表组件
│   │   ├── SessionList.vue       # 会话列表组件
│   │   ├── SessionItem.vue       # 会话项组件
│   │   ├── MarkdownRenderer.vue  # Markdown渲染组件
│   │   ├── CodeBlock.vue         # 代码块组件
│   │   ├── AttachmentPreview.vue # 附件预览组件
│   │   └── TypingIndicator.vue   # 输入状态指示器
│   ├── knowledge/                # 知识库模块组件
│   │   ├── KnowledgeContainer.vue # 知识库容器组件
│   │   ├── KnowledgeList.vue     # 知识库列表组件
│   │   ├── KnowledgeItem.vue     # 知识库项组件
│   │   ├── DocumentList.vue      # 文档列表组件
│   │   ├── DocumentItem.vue      # 文档项组件
│   │   ├── DocumentUpload.vue    # 文档上传组件
│   │   ├── DocumentPreview.vue   # 文档预览组件
│   │   ├── SearchResults.vue     # 搜索结果组件
│   │   ├── VectorSearch.vue      # 向量搜索组件
│   │   └── KnowledgeGraph.vue    # 知识图谱组件
│   ├── model/                    # 模型管理模块组件
│   │   ├── ModelContainer.vue    # 模型容器组件
│   │   ├── ModelList.vue         # 模型列表组件
│   │   ├── ModelItem.vue         # 模型项组件
│   │   ├── ModelSearch.vue       # 模型搜索组件
│   │   ├── ModelDownload.vue     # 模型下载组件
│   │   ├── ModelUpload.vue       # 模型上传组件
│   │   ├── ModelConfig.vue       # 模型配置组件
│   │   ├── ModelPerformance.vue  # 模型性能监控组件
│   │   ├── DownloadProgress.vue  # 下载进度组件
│   │   └── ModelQuantization.vue # 模型量化组件
│   ├── multimodal/               # 多模态模块组件
│   │   ├── MultimodalContainer.vue # 多模态容器组件
│   │   ├── OCRProcessor.vue      # OCR处理组件
│   │   ├── TTSProcessor.vue      # TTS处理组件
│   │   ├── ASRProcessor.vue      # ASR处理组件
│   │   ├── ImageAnalyzer.vue     # 图像分析组件
│   │   ├── VideoAnalyzer.vue     # 视频分析组件
│   │   ├── FormatConverter.vue   # 格式转换组件
│   │   ├── TaskQueue.vue         # 任务队列组件
│   │   ├── ProcessingHistory.vue # 处理历史组件
│   │   └── MediaPreview.vue      # 媒体预览组件
│   ├── remote/                   # 远程配置模块组件
│   │   ├── RemoteContainer.vue   # 远程配置容器组件
│   │   ├── ConfigList.vue        # 配置列表组件
│   │   ├── ConfigItem.vue        # 配置项组件
│   │   ├── ConfigForm.vue        # 配置表单组件
│   │   ├── ApiKeyManager.vue     # API密钥管理组件
│   │   ├── ProxySettings.vue     # 代理设置组件
│   │   ├── ConfigTemplates.vue   # 配置模板组件
│   │   ├── ConfigTest.vue        # 配置测试组件
│   │   └── ConfigSync.vue        # 配置同步组件
│   ├── network/                  # 网络共享模块组件
│   │   ├── NetworkContainer.vue  # 网络容器组件
│   │   ├── DeviceList.vue        # 设备列表组件
│   │   ├── DeviceItem.vue        # 设备项组件
│   │   ├── ResourceShare.vue     # 资源共享组件
│   │   ├── TransferList.vue      # 传输列表组件
│   │   ├── TransferItem.vue      # 传输项组件
│   │   ├── P2PConnection.vue     # P2P连接组件
│   │   ├── NetworkSettings.vue   # 网络设置组件
│   │   └── SecuritySettings.vue  # 安全设置组件
│   ├── plugins/                  # 插件系统组件
│   │   ├── PluginContainer.vue   # 插件容器组件
│   │   ├── PluginList.vue        # 插件列表组件
│   │   ├── PluginItem.vue        # 插件项组件
│   │   ├── PluginMarket.vue      # 插件市场组件
│   │   ├── PluginConfig.vue      # 插件配置组件
│   │   ├── PluginLogs.vue        # 插件日志组件
│   │   ├── PluginSandbox.vue     # 插件沙箱组件
│   │   └── PluginPermissions.vue # 插件权限组件
│   └── settings/                 # 设置模块组件
│       ├── SettingsContainer.vue # 设置容器组件
│       ├── GeneralSettings.vue   # 通用设置组件
│       ├── AppearanceSettings.vue # 外观设置组件
│       ├── LanguageSettings.vue  # 语言设置组件
│       ├── PerformanceSettings.vue # 性能设置组件
│       ├── SecuritySettings.vue  # 安全设置组件
│       ├── BackupSettings.vue    # 备份设置组件
│       ├── UpdateSettings.vue    # 更新设置组件
│       └── AboutDialog.vue       # 关于对话框组件
├── composables/                  # 组合式函数 - 可复用的业务逻辑
│   ├── useAuth.ts                # 用户认证相关逻辑
│   ├── useTheme.ts               # 主题切换相关逻辑
│   ├── useI18n.ts                # 国际化相关逻辑
│   ├── useChat.ts                # 聊天功能相关逻辑
│   ├── useKnowledge.ts           # 知识库功能相关逻辑
│   ├── useModel.ts               # 模型管理相关逻辑
│   ├── useNetwork.ts             # 网络共享相关逻辑
│   ├── useMultimodal.ts          # 多模态处理相关逻辑
│   ├── usePlugin.ts              # 插件系统相关逻辑
│   ├── useWebSocket.ts           # WebSocket连接相关逻辑
│   ├── useEventSource.ts         # SSE事件流相关逻辑
│   ├── useFileUpload.ts          # 文件上传相关逻辑
│   ├── useDownload.ts            # 文件下载相关逻辑
│   ├── useNotification.ts        # 通知相关逻辑
│   └── useStorage.ts             # 本地存储相关逻辑
├── stores/                       # 状态管理 (Pinia) - 全局状态和业务逻辑
│   ├── index.ts                  # Store统一导出
│   ├── app.ts                    # 应用全局状态
│   ├── auth.ts                   # 用户认证状态
│   ├── theme.ts                  # 主题状态管理
│   ├── i18n.ts                   # 国际化状态管理
│   ├── chat.ts                   # 聊天状态管理
│   ├── knowledge.ts              # 知识库状态管理
│   ├── model.ts                  # 模型管理状态
│   ├── remote.ts                 # 远程配置状态
│   ├── network.ts                # 网络共享状态
│   ├── multimodal.ts             # 多模态处理状态
│   ├── plugin.ts                 # 插件系统状态
│   └── system.ts                 # 系统状态管理
├── views/                        # 页面视图 - 主导航页面
│   ├── Chat/                     # 聊天页面
│   │   ├── index.vue             # 聊天主页面
│   │   ├── SessionView.vue       # 会话视图页面
│   │   └── SettingsView.vue      # 聊天设置页面
│   ├── Knowledge/                # 知识库页面
│   │   ├── index.vue             # 知识库主页面
│   │   ├── DocumentView.vue      # 文档视图页面
│   │   ├── SearchView.vue        # 搜索视图页面
│   │   └── AnalyticsView.vue     # 分析视图页面
│   ├── Model/                    # 模型管理页面
│   │   ├── index.vue             # 模型管理主页面
│   │   ├── LocalModels.vue       # 本地模型页面
│   │   ├── OnlineModels.vue      # 在线模型页面
│   │   ├── DownloadCenter.vue    # 下载中心页面
│   │   └── PerformanceView.vue   # 性能监控页面
│   ├── Multimodal/               # 多模态页面
│   │   ├── index.vue             # 多模态主页面
│   │   ├── OCRView.vue           # OCR处理页面
│   │   ├── AudioView.vue         # 音频处理页面
│   │   ├── ImageView.vue         # 图像处理页面
│   │   └── VideoView.vue         # 视频处理页面
│   ├── Remote/                   # 远程配置页面
│   │   ├── index.vue             # 远程配置主页面
│   │   ├── ApiKeys.vue           # API密钥管理页面
│   │   ├── ProxyConfig.vue       # 代理配置页面
│   │   └── CloudSync.vue         # 云端同步页面
│   ├── Network/                  # 网络共享页面
│   │   ├── index.vue             # 网络共享主页面
│   │   ├── DeviceManager.vue     # 设备管理页面
│   │   ├── ResourceShare.vue     # 资源共享页面
│   │   └── TransferCenter.vue    # 传输中心页面
│   ├── Plugins/                  # 插件页面
│   │   ├── index.vue             # 插件主页面
│   │   ├── Market.vue            # 插件市场页面
│   │   ├── Installed.vue         # 已安装插件页面
│   │   └── Developer.vue         # 开发者页面
│   └── Settings/                 # 设置页面
│       ├── index.vue             # 设置主页面
│       ├── General.vue           # 通用设置页面
│       ├── Appearance.vue        # 外观设置页面
│       ├── Performance.vue       # 性能设置页面
│       └── Security.vue          # 安全设置页面
├── router/                       # 路由管理
│   ├── index.ts                  # 路由配置主文件
│   ├── routes.ts                 # 路由定义
│   ├── guards.ts                 # 路由守卫
│   └── modules/                  # 模块化路由
│       ├── chat.ts               # 聊天模块路由
│       ├── knowledge.ts          # 知识库模块路由
│       ├── model.ts              # 模型管理模块路由
│       ├── multimodal.ts         # 多模态模块路由
│       ├── remote.ts             # 远程配置模块路由
│       ├── network.ts            # 网络共享模块路由
│       ├── plugins.ts            # 插件模块路由
│       └── settings.ts           # 设置模块路由
├── utils/                        # 工具函数 - 通用工具和辅助函数
│   ├── index.ts                  # 工具函数统一导出
│   ├── request.ts                # HTTP请求工具
│   ├── websocket.ts              # WebSocket工具
│   ├── eventSource.ts            # SSE事件流工具
│   ├── storage.ts                # 本地存储工具
│   ├── crypto.ts                 # 加密解密工具
│   ├── file.ts                   # 文件处理工具
│   ├── format.ts                 # 格式化工具
│   ├── validation.ts             # 数据验证工具
│   ├── date.ts                   # 日期时间工具
│   ├── color.ts                  # 颜色处理工具
│   ├── device.ts                 # 设备信息工具
│   ├── performance.ts            # 性能监控工具
│   └── constants.ts              # 常量定义
├── types/                        # TypeScript类型定义
│   ├── index.ts                  # 类型定义统一导出
│   ├── api.ts                    # API接口类型定义
│   ├── chat.ts                   # 聊天相关类型定义
│   ├── knowledge.ts              # 知识库相关类型定义
│   ├── model.ts                  # 模型相关类型定义
│   ├── multimodal.ts             # 多模态相关类型定义
│   ├── remote.ts                 # 远程配置相关类型定义
│   ├── network.ts                # 网络共享相关类型定义
│   ├── plugin.ts                 # 插件相关类型定义
│   ├── system.ts                 # 系统相关类型定义
│   ├── user.ts                   # 用户相关类型定义
│   └── common.ts                 # 通用类型定义
├── locales/                      # 国际化文件
│   ├── index.ts                  # 国际化配置主文件
│   ├── zh-CN/                    # 中文语言包
│   │   ├── common.json           # 通用翻译
│   │   ├── chat.json             # 聊天模块翻译
│   │   ├── knowledge.json        # 知识库模块翻译
│   │   ├── model.json            # 模型管理模块翻译
│   │   ├── multimodal.json       # 多模态模块翻译
│   │   ├── remote.json           # 远程配置模块翻译
│   │   ├── network.json          # 网络共享模块翻译
│   │   ├── plugins.json          # 插件模块翻译
│   │   └── settings.json         # 设置模块翻译
│   └── en-US/                    # 英文语言包
│       ├── common.json           # 通用翻译
│       ├── chat.json             # 聊天模块翻译
│       ├── knowledge.json        # 知识库模块翻译
│       ├── model.json            # 模型管理模块翻译
│       ├── multimodal.json       # 多模态模块翻译
│       ├── remote.json           # 远程配置模块翻译
│       ├── network.json          # 网络共享模块翻译
│       ├── plugins.json          # 插件模块翻译
│       └── settings.json         # 设置模块翻译
├── directives/                   # Vue指令
│   ├── index.ts                  # 指令统一导出
│   ├── loading.ts                # 加载指令
│   ├── permission.ts             # 权限指令
│   ├── copy.ts                   # 复制指令
│   └── resize.ts                 # 尺寸变化指令
├── App.vue                       # 根组件
├── main.ts                       # 应用入口文件
└── env.d.ts                      # 环境变量类型定义
```
