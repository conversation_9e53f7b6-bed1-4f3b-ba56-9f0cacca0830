# AI Studio 完整方案设计

## 1. 项目概述

### 1.1 项目背景

AI Studio 是一个基于 Vue3 + TypeScript + Vite + Tauri 2.x 技术栈的中文AI助手桌面应用，专为 Windows/macOS 平台设计。项目旨在提供本地大模型部署、知识库管理、局域网共享等企业级功能，实现完全离线的AI助手解决方案。

### 1.2 项目目标

- **本地化部署**：支持本地大模型推理，无需依赖云端服务
- **知识库管理**：提供文档解析、向量搜索、RAG增强等功能
- **局域网协作**：实现设备间模型、知识库、配置的共享
- **多模态支持**：集成OCR、TTS、语音识别等多媒体处理能力
- **企业级质量**：提供生产环境可用的稳定性和性能
- **插件生态**：支持第三方插件扩展和云端模型API集成

### 1.3 核心功能特性

1. **智能对话系统**
   - 支持流式对话、多模态输入、会话管理
   - Markdown渲染、代码高亮、数学公式支持
   - 多会话并行处理、会话历史持久化

2. **知识库管理系统**
   - 支持PDF、Word、TXT等多种格式文档上传
   - 基于ChromaDB实现向量检索和RAG增强
   - 智能文档分块、语义搜索、知识图谱构建

3. **模型管理系统**
   - 本地模型加载卸载、在线模型下载
   - 性能监控、模型量化、GPU加速支持
   - HuggingFace集成、断点续传下载

4. **多模态处理系统**
   - 图像识别、语音识别(STT)、语音合成(TTS)
   - OCR文字识别、视频分析、格式转换
   - 批量处理、任务队列管理

5. **远程配置系统**
   - API密钥管理、远程模型配置、代理设置
   - 支持OpenAI、Anthropic、Google等主流AI服务商
   - 配置同步、安全存储、连通性验证

6. **局域网共享系统**
   - mDNS设备发现、P2P文件传输、分布式推理
   - 资源共享、权限管理、安全认证
   - 跨设备协同、实时同步

7. **插件扩展系统**
   - WASM插件运行环境、插件市场
   - 沙箱隔离、权限管理、热插拔支持
   - 自定义API集成、JavaScript脚本支持

8. **系统管理功能**
   - 主题切换、语言切换、用户信息管理
   - 性能监控、日志管理、自动更新
   - 数据备份、健康检查、故障诊断

## 2. 技术架构

### 2.1 整体架构设计

AI Studio 采用现代化的桌面应用架构，基于 Tauri 框架实现跨平台支持：

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
│  Vue3 + TypeScript + Vite + Naive UI + Tailwind CSS       │
├─────────────────────────────────────────────────────────────┤
│                    应用层 (Application)                     │
│              Tauri 2.x + Rust Backend                     │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (Services)                       │
│  AI推理引擎 │ 向量数据库 │ 文件处理 │ 网络通信 │ 插件系统    │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data)                           │
│     SQLite 主数据库 │ ChromaDB 向量库 │ 本地文件存储       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 前端技术栈

**核心框架**
- **Vue 3.4+**：采用 Composition API，提供响应式数据管理
- **TypeScript 5.0+**：强类型支持，提升代码质量和开发效率
- **Vite 7.0+**：快速构建工具，支持热更新和模块化开发

**UI 组件库**
- **Naive UI 2.0+**：现代化 Vue3 组件库，提供丰富的UI组件
- **Tailwind CSS 3.0+**：原子化CSS框架，快速样式开发
- **SCSS**：CSS预处理器，支持变量和混入

**状态管理**
- **Pinia 2.0+**：Vue3 官方推荐的状态管理库
- **Vue Router 4.0+**：单页应用路由管理

**工具库**
- **Vue I18n 9.0+**：国际化支持
- **Iconify**：图标库，提供丰富的图标资源
- **@vueuse/markdown**：Markdown渲染支持
- **Prism.js**：代码语法高亮
- **ECharts 5.0+**：数据可视化图表库

### 2.3 后端技术栈

**核心框架**
- **Tauri 2.x**：跨平台桌面应用框架
- **Rust 1.75+**：系统级编程语言，提供高性能和内存安全

**数据存储**
- **SQLite 3.45+**：轻量级关系型数据库，用于主要数据存储
- **ChromaDB**：向量数据库，用于知识库向量存储和检索

**AI 推理引擎**
- **Candle-core**：Rust原生AI推理框架
- **llama.cpp**：高性能大语言模型推理引擎

**网络通信**
- **Tokio**：异步运行时，支持高并发网络操作
- **Reqwest**：HTTP客户端库
- **mdns**：多播DNS服务发现
- **tokio-tungstenite**：WebSocket支持

**文档处理**
- **pdf-extract**：PDF文档解析
- **docx-rs**：Word文档处理
- **calamine**：Excel文档处理

**多媒体处理**
- **image + imageproc**：图像处理和分析
- **rodio**：音频播放和处理
- **whisper-rs**：语音识别引擎

**工具库**
- **Serde + serde_json**：序列化和反序列化
- **thiserror + anyhow**：错误处理
- **tracing + tracing-subscriber**：日志系统
- **aes-gcm + ring**：加密和安全

### 2.4 开发工具链

**包管理**
- **npm/pnpm**：前端包管理器
- **Cargo**：Rust包管理器

**代码质量**
- **Prettier**：前端代码格式化
- **ESLint**：前端代码检查
- **rustfmt**：Rust代码格式化
- **Clippy**：Rust代码检查

**测试框架**
- **Vitest**：前端单元测试
- **cargo test**：Rust单元测试

**构建工具**
- **Tauri CLI**：应用构建和打包
- **Git + Git LFS**：版本控制（支持大文件）

## 3. 系统设计

### 3.1 数据库设计

**主数据库 (SQLite)**

系统采用 SQLite 作为主数据库，存储应用的核心业务数据：

```sql
-- 会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    model_id TEXT,
    system_prompt TEXT,
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2048,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY,
    session_id TEXT REFERENCES chat_sessions(id),
    role TEXT NOT NULL, -- 'user' | 'assistant' | 'system'
    content TEXT NOT NULL,
    attachments JSON,
    tokens_used INTEGER,
    response_time REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    embedding_model TEXT DEFAULT 'sentence-transformers/all-MiniLM-L6-v2',
    chunk_size INTEGER DEFAULT 512,
    chunk_overlap INTEGER DEFAULT 50,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    kb_id TEXT REFERENCES knowledge_bases(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- 'pdf', 'docx', 'txt', 'md'
    size INTEGER,
    path TEXT NOT NULL,
    status TEXT DEFAULT 'processing', -- 'processing', 'completed', 'failed'
    chunks_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    author TEXT,
    version TEXT,
    size INTEGER,
    quantization TEXT, -- 'none', 'q4_0', 'q4_1', 'q8_0'
    architecture TEXT, -- 'llama', 'mistral', 'qwen'
    context_length INTEGER DEFAULT 2048,
    status TEXT DEFAULT 'available', -- 'available', 'downloading', 'loaded', 'error'
    local_path TEXT,
    download_url TEXT,
    huggingface_id TEXT,
    config JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**向量数据库 (ChromaDB)**

用于存储文档向量和实现语义搜索：

- **Collection 结构**：每个知识库对应一个 Collection
- **向量维度**：根据 embedding 模型确定（通常为 384 或 768 维）
- **元数据存储**：文档ID、块索引、页码等信息
- **距离计算**：使用余弦相似度进行语义匹配

### 3.2 安全设计

**数据加密**
- **敏感数据加密**：API密钥、代理密码等使用 AES-GCM 加密存储
- **传输加密**：网络通信使用 TLS 1.3 加密
- **本地存储**：支持数据库文件加密选项

**权限管理**
- **插件沙箱**：插件运行在隔离环境中，限制系统访问
- **网络权限**：细粒度控制网络访问权限
- **文件权限**：限制文件系统访问范围

**身份认证**
- **设备认证**：局域网设备间使用公钥加密认证
- **API认证**：支持多种API认证方式（API Key、OAuth等）
- **会话管理**：安全的会话状态管理

### 3.3 性能设计

**内存管理**
- **模型加载优化**：支持模型分层加载，减少内存占用
- **缓存策略**：智能缓存常用数据，提升响应速度
- **垃圾回收**：定期清理无用数据和临时文件

**并发处理**
- **异步架构**：基于 Tokio 的异步处理，支持高并发
- **任务队列**：后台任务队列处理耗时操作
- **资源池**：数据库连接池、HTTP连接池等资源复用

**性能监控**
- **实时监控**：CPU、内存、GPU使用率实时监控
- **性能指标**：推理速度、响应时间等关键指标
- **性能优化**：自动性能调优和资源分配

## 4. 功能模块设计

### 4.1 聊天模块 (Chat)

**功能描述**
提供AI对话、会话管理、流式响应等核心聊天功能，支持多模态输入和智能上下文管理。

**核心特性**
- **流式响应**：基于SSE (Server-Sent Events) 实现实时token流输出
- **会话管理**：支持多会话并行、会话历史持久化、会话导出
- **RAG集成**：自动检索相关知识库内容增强回复质量
- **多模态输入**：支持文本、图片、音频等多种输入方式
- **Markdown渲染**：支持代码高亮、数学公式、表格等富文本显示

**技术实现**
- **前端**：Vue3 + TypeScript，使用 EventSource 接收流式数据
- **后端**：Rust + Tokio，异步处理推理请求
- **数据存储**：SQLite 存储会话和消息数据
- **推理引擎**：支持本地模型（Candle/llama.cpp）和远程API

**API接口**
```typescript
// 发送消息
POST /api/chat/send
Request: {
  session_id: string;
  message: string;
  attachments?: Array<{type: string; url: string; name: string}>;
  model_config?: {model_id: string; temperature: number; max_tokens: number};
}

// 流式响应
GET /api/chat/stream/{message_id}
Response: Server-Sent Events

// 会话管理
GET /api/chat/sessions
POST /api/chat/sessions
PUT /api/chat/sessions/{id}
DELETE /api/chat/sessions/{id}
```

### 4.2 知识库模块 (Knowledge Base)

**功能描述**
提供文档管理、向量搜索、RAG检索等知识库功能，支持多种文档格式和智能语义搜索。

**核心特性**
- **文档解析**：支持PDF、Word、Excel、Markdown、TXT等多种格式
- **向量化存储**：使用ChromaDB进行向量存储和检索
- **增量索引**：支持文档变更检测和增量更新
- **语义搜索**：基于embedding模型的高质量语义搜索
- **文档切分**：智能文档分块，保持语义完整性
- **知识图谱**：构建实体关系图，增强检索效果

**技术实现**
- **文档解析**：pdf-extract、docx-rs、calamine等Rust库
- **向量化**：sentence-transformers模型生成文档向量
- **存储**：ChromaDB向量数据库 + SQLite元数据存储
- **搜索**：余弦相似度计算 + 重排序算法

**API接口**
```typescript
// 知识库管理
GET /api/knowledge
POST /api/knowledge
PUT /api/knowledge/{id}
DELETE /api/knowledge/{id}

// 文档管理
POST /api/knowledge/{id}/upload
GET /api/knowledge/{id}/documents
DELETE /api/knowledge/{kb_id}/documents/{doc_id}

// 语义搜索
POST /api/knowledge/{id}/search
Request: {
  query: string;
  limit?: number;
  threshold?: number;
  filters?: object;
}
```

### 4.3 模型管理模块 (Model Management)

**功能描述**
提供本地模型下载、加载、量化、部署等功能，支持HuggingFace生态和多种推理引擎。

**核心特性**
- **HuggingFace集成**：支持从HF Hub下载模型，包含镜像站切换
- **断点续传**：支持大文件分片下载和自动恢复
- **模型量化**：集成GPTQ、AWQ等量化工具，减少内存占用
- **GPU加速**：支持CUDA、Metal等GPU加速框架
- **一键部署**：自动化模型部署和服务管理
- **性能监控**：实时监控模型推理性能和资源使用

**技术实现**
- **下载引擎**：多线程分片下载，支持断点续传
- **模型加载**：Candle-core和llama.cpp双引擎支持
- **量化工具**：集成主流量化算法
- **监控系统**：实时性能指标收集和分析

**API接口**
```typescript
// 模型管理
GET /api/models
GET /api/models/search
POST /api/models/download
POST /api/models/upload
DELETE /api/models/{id}

// 模型操作
POST /api/models/{id}/load
POST /api/models/{id}/unload
POST /api/models/{id}/quantize

// 下载管理
GET /api/models/downloads
POST /api/models/downloads/{id}/pause
POST /api/models/downloads/{id}/resume
```

### 4.4 多模态处理模块 (Multimodal)

**功能描述**
提供OCR识别、语音处理、图像分析等多模态功能，支持多种媒体格式处理和批量任务管理。

**核心特性**
- **OCR识别**：集成Tesseract或PaddleOCR进行文字识别
- **语音处理**：支持语音转文字(ASR)和文字转语音(TTS)
- **图像分析**：集成YOLO、CLIP等模型进行图像理解
- **视频处理**：支持视频分析和字幕生成
- **格式转换**：支持多种音视频格式转换
- **批量处理**：支持批量文件处理和任务队列

**技术实现**
- **OCR引擎**：Tesseract + 自定义优化算法
- **语音引擎**：whisper-rs + rodio音频处理
- **图像处理**：image + imageproc库
- **任务队列**：异步任务调度和进度管理

**API接口**
```typescript
// 多模态处理
POST /api/multimodal/ocr
POST /api/multimodal/tts
POST /api/multimodal/asr
POST /api/multimodal/image/analyze
POST /api/multimodal/video/analyze
POST /api/multimodal/convert

// 任务管理
GET /api/multimodal/tasks
GET /api/multimodal/tasks/{id}
DELETE /api/multimodal/tasks/{id}
GET /api/multimodal/history
```

### 4.5 远程配置模块 (Remote Configuration)

**功能描述**
提供API密钥管理、远程模型配置、代理设置等功能，支持多种AI服务商和配置同步。

**核心特性**
- **API密钥管理**：支持OpenAI、Anthropic、Google等主流AI服务商
- **代理配置**：支持HTTP/HTTPS/SOCKS5代理设置
- **配置同步**：支持配置云端备份和多设备同步
- **安全存储**：敏感信息加密存储，支持主密码保护
- **配置模板**：预设常用配置模板，快速配置
- **配置验证**：自动验证API密钥有效性和网络连通性

**技术实现**
- **加密存储**：AES-GCM加密敏感配置信息
- **代理支持**：reqwest + proxy配置
- **配置验证**：异步连通性测试
- **模板系统**：预定义配置模板

**API接口**
```typescript
// 配置管理
GET /api/remote/configs
POST /api/remote/configs
PUT /api/remote/configs/{id}
DELETE /api/remote/configs/{id}
POST /api/remote/configs/{id}/test
POST /api/remote/configs/{id}/activate

// 模板管理
GET /api/remote/templates
POST /api/remote/templates

// 配置同步
POST /api/remote/sync/backup
POST /api/remote/sync/restore
```

### 4.6 局域网共享模块 (Network Sharing)

**功能描述**
提供局域网设备发现、P2P通信、资源共享等功能，实现设备间的协同工作。

**核心特性**
- **mDNS发现**：基于mDNS协议的局域网设备自动发现
- **P2P通信**：WebRTC或自定义协议的点对点通信
- **文件传输**：支持大文件分片传输和断点续传
- **权限管理**：细粒度的访问控制和安全认证
- **资源共享**：模型、知识库、配置的跨设备共享
- **分布式推理**：多设备协同推理，提升性能

**技术实现**
- **设备发现**：mdns库实现服务发现
- **P2P通信**：自定义协议 + TLS加密
- **文件传输**：分片传输 + 校验和验证
- **权限控制**：基于公钥的身份认证

**API接口**
```typescript
// 设备管理
GET /api/network/discover
POST /api/network/connect
POST /api/network/disconnect
GET /api/network/nodes

// 资源共享
POST /api/network/share
GET /api/network/resources

// 传输管理
POST /api/network/transfer
GET /api/network/transfers
POST /api/network/transfers/{id}/pause
POST /api/network/transfers/{id}/resume
```

### 4.7 插件系统模块 (Plugin System)

**功能描述**
支持第三方插件扩展、插件市场、云端API集成等功能，提供安全的插件运行环境。

**核心特性**
- **WASM插件**：基于WebAssembly的安全插件运行环境
- **插件市场**：在线插件商店，支持搜索、安装、更新
- **API集成**：支持自定义API接口和JavaScript脚本
- **沙箱隔离**：插件运行在隔离环境中，确保系统安全
- **权限管理**：细粒度的插件权限控制
- **热插拔**：支持插件的动态加载和卸载

**技术实现**
- **WASM运行时**：wasmtime + 安全沙箱
- **权限系统**：基于能力的权限模型
- **插件API**：标准化插件接口定义
- **市场服务**：插件发现和分发服务

**API接口**
```typescript
// 插件管理
GET /api/plugins
POST /api/plugins/install
DELETE /api/plugins/{id}
POST /api/plugins/{id}/enable
POST /api/plugins/{id}/disable

// 插件配置
GET /api/plugins/{id}/config
PUT /api/plugins/{id}/config

// 插件执行
POST /api/plugins/{id}/execute

// 插件市场
GET /api/plugins/market
GET /api/plugins/market/search
POST /api/plugins/market/{id}/install
```

### 4.8 系统管理模块 (System Management)

**功能描述**
提供系统监控、配置管理、日志管理等系统功能，确保应用稳定运行。

**核心特性**
- **性能监控**：实时监控CPU、内存、GPU使用情况
- **配置管理**：统一的配置文件管理和热更新
- **日志系统**：结构化日志记录和查询
- **自动更新**：应用自动更新和版本管理
- **数据备份**：自动数据备份和恢复机制
- **系统诊断**：健康检查和故障诊断

**技术实现**
- **监控系统**：tracing + 自定义指标收集
- **配置热更新**：文件监听 + 动态重载
- **备份系统**：增量备份 + 压缩存储
- **更新机制**：差分更新 + 签名验证

**API接口**
```typescript
// 系统信息
GET /api/system/info
GET /api/system/performance
GET /api/system/health

// 配置管理
GET /api/system/configs
PUT /api/system/configs

// 日志管理
GET /api/system/logs

// 备份管理
POST /api/system/backup
GET /api/system/backups
POST /api/system/restore

// 更新管理
POST /api/system/update/check
POST /api/system/update/install
```

## 5. 项目结构

### 5.1 前端目录结构 (src/)

```
src/
├── api/                          # API服务层 - 封装所有后台接口调用
│   ├── base.ts                   # 基础API配置和拦截器
│   ├── chat.ts                   # 聊天相关API接口
│   ├── knowledge.ts              # 知识库相关API接口
│   ├── model.ts                  # 模型管理相关API接口
│   ├── multimodal.ts             # 多模态处理相关API接口
│   ├── network.ts                # 网络共享相关API接口
│   ├── remote.ts                 # 远程配置相关API接口
│   ├── system.ts                 # 系统管理相关API接口
│   ├── plugin.ts                 # 插件系统相关API接口
│   └── index.ts                  # API模块统一导出
├── assets/                       # 静态资源文件
│   ├── images/                   # 图片资源
│   │   ├── icons/                # 图标文件
│   │   ├── logos/                # Logo文件
│   │   └── backgrounds/          # 背景图片
│   ├── fonts/                    # 字体文件
│   └── styles/                   # 全局样式文件
│       ├── variables.scss        # SCSS变量定义
│       ├── mixins.scss           # SCSS混入函数
│       ├── reset.scss            # 样式重置
│       ├── themes/               # 主题样式
│       │   ├── light.scss        # 浅色主题
│       │   └── dark.scss         # 深色主题
│       └── global.scss           # 全局样式
├── components/                   # 可复用组件 - 按功能模块组织
│   ├── common/                   # 通用组件
│   │   ├── AppHeader.vue         # 应用头部导航组件
│   │   ├── AppSidebar.vue        # 应用侧边栏组件
│   │   ├── AppFooter.vue         # 应用底部组件
│   │   ├── LoadingSpinner.vue    # 加载动画组件
│   │   ├── ErrorBoundary.vue     # 错误边界组件
│   │   ├── ConfirmDialog.vue     # 确认对话框组件
│   │   ├── FileUpload.vue        # 文件上传组件
│   │   ├── ProgressBar.vue       # 进度条组件
│   │   ├── SearchInput.vue       # 搜索输入组件
│   │   ├── ThemeToggle.vue       # 主题切换组件
│   │   ├── LanguageSwitch.vue    # 语言切换组件
│   │   └── UserDropdown.vue      # 用户下拉菜单组件
│   ├── chat/                     # 聊天模块组件
│   │   ├── ChatContainer.vue     # 聊天容器组件
│   │   ├── ChatSidebar.vue       # 聊天侧边栏组件
│   │   ├── ChatInput.vue         # 聊天输入组件
│   │   ├── ChatMessage.vue       # 聊天消息组件
│   │   ├── MessageList.vue       # 消息列表组件
│   │   ├── SessionList.vue       # 会话列表组件
│   │   ├── SessionItem.vue       # 会话项组件
│   │   ├── MarkdownRenderer.vue  # Markdown渲染组件
│   │   ├── CodeBlock.vue         # 代码块组件
│   │   ├── AttachmentPreview.vue # 附件预览组件
│   │   └── TypingIndicator.vue   # 输入状态指示器
│   ├── knowledge/                # 知识库模块组件
│   │   ├── KnowledgeContainer.vue # 知识库容器组件
│   │   ├── KnowledgeList.vue     # 知识库列表组件
│   │   ├── KnowledgeItem.vue     # 知识库项组件
│   │   ├── DocumentList.vue      # 文档列表组件
│   │   ├── DocumentItem.vue      # 文档项组件
│   │   ├── DocumentUpload.vue    # 文档上传组件
│   │   ├── DocumentPreview.vue   # 文档预览组件
│   │   ├── SearchResults.vue     # 搜索结果组件
│   │   ├── VectorSearch.vue      # 向量搜索组件
│   │   └── KnowledgeGraph.vue    # 知识图谱组件
│   ├── model/                    # 模型管理模块组件
│   │   ├── ModelContainer.vue    # 模型容器组件
│   │   ├── ModelList.vue         # 模型列表组件
│   │   ├── ModelItem.vue         # 模型项组件
│   │   ├── ModelSearch.vue       # 模型搜索组件
│   │   ├── ModelDownload.vue     # 模型下载组件
│   │   ├── ModelUpload.vue       # 模型上传组件
│   │   ├── ModelConfig.vue       # 模型配置组件
│   │   ├── ModelPerformance.vue  # 模型性能监控组件
│   │   ├── DownloadProgress.vue  # 下载进度组件
│   │   └── ModelQuantization.vue # 模型量化组件
│   ├── multimodal/               # 多模态模块组件
│   │   ├── MultimodalContainer.vue # 多模态容器组件
│   │   ├── OCRProcessor.vue      # OCR处理组件
│   │   ├── TTSProcessor.vue      # TTS处理组件
│   │   ├── ASRProcessor.vue      # ASR处理组件
│   │   ├── ImageAnalyzer.vue     # 图像分析组件
│   │   ├── VideoAnalyzer.vue     # 视频分析组件
│   │   ├── FormatConverter.vue   # 格式转换组件
│   │   ├── TaskQueue.vue         # 任务队列组件
│   │   ├── ProcessingHistory.vue # 处理历史组件
│   │   └── MediaPreview.vue      # 媒体预览组件
│   ├── remote/                   # 远程配置模块组件
│   │   ├── RemoteContainer.vue   # 远程配置容器组件
│   │   ├── ConfigList.vue        # 配置列表组件
│   │   ├── ConfigItem.vue        # 配置项组件
│   │   ├── ConfigForm.vue        # 配置表单组件
│   │   ├── ApiKeyManager.vue     # API密钥管理组件
│   │   ├── ProxySettings.vue     # 代理设置组件
│   │   ├── ConfigTemplates.vue   # 配置模板组件
│   │   ├── ConfigTest.vue        # 配置测试组件
│   │   └── ConfigSync.vue        # 配置同步组件
│   ├── network/                  # 网络共享模块组件
│   │   ├── NetworkContainer.vue  # 网络容器组件
│   │   ├── DeviceList.vue        # 设备列表组件
│   │   ├── DeviceItem.vue        # 设备项组件
│   │   ├── ResourceShare.vue     # 资源共享组件
│   │   ├── TransferList.vue      # 传输列表组件
│   │   ├── TransferItem.vue      # 传输项组件
│   │   ├── P2PConnection.vue     # P2P连接组件
│   │   ├── NetworkSettings.vue   # 网络设置组件
│   │   └── SecuritySettings.vue  # 安全设置组件
│   ├── plugins/                  # 插件系统组件
│   │   ├── PluginContainer.vue   # 插件容器组件
│   │   ├── PluginList.vue        # 插件列表组件
│   │   ├── PluginItem.vue        # 插件项组件
│   │   ├── PluginMarket.vue      # 插件市场组件
│   │   ├── PluginConfig.vue      # 插件配置组件
│   │   ├── PluginLogs.vue        # 插件日志组件
│   │   ├── PluginSandbox.vue     # 插件沙箱组件
│   │   └── PluginPermissions.vue # 插件权限组件
│   └── settings/                 # 设置模块组件
│       ├── SettingsContainer.vue # 设置容器组件
│       ├── GeneralSettings.vue   # 通用设置组件
│       ├── AppearanceSettings.vue # 外观设置组件
│       ├── LanguageSettings.vue  # 语言设置组件
│       ├── PerformanceSettings.vue # 性能设置组件
│       ├── SecuritySettings.vue  # 安全设置组件
│       ├── BackupSettings.vue    # 备份设置组件
│       ├── UpdateSettings.vue    # 更新设置组件
│       └── AboutDialog.vue       # 关于对话框组件
├── composables/                  # 组合式函数 - 可复用的业务逻辑
│   ├── useAuth.ts                # 用户认证相关逻辑
│   ├── useTheme.ts               # 主题切换相关逻辑
│   ├── useI18n.ts                # 国际化相关逻辑
│   ├── useChat.ts                # 聊天功能相关逻辑
│   ├── useKnowledge.ts           # 知识库功能相关逻辑
│   ├── useModel.ts               # 模型管理相关逻辑
│   ├── useNetwork.ts             # 网络共享相关逻辑
│   ├── useMultimodal.ts          # 多模态处理相关逻辑
│   ├── usePlugin.ts              # 插件系统相关逻辑
│   ├── useWebSocket.ts           # WebSocket连接相关逻辑
│   ├── useEventSource.ts         # SSE事件流相关逻辑
│   ├── useFileUpload.ts          # 文件上传相关逻辑
│   ├── useDownload.ts            # 文件下载相关逻辑
│   ├── useNotification.ts        # 通知相关逻辑
│   └── useStorage.ts             # 本地存储相关逻辑
├── stores/                       # 状态管理 (Pinia) - 全局状态和业务逻辑
│   ├── index.ts                  # Store统一导出
│   ├── app.ts                    # 应用全局状态
│   ├── auth.ts                   # 用户认证状态
│   ├── theme.ts                  # 主题状态管理
│   ├── i18n.ts                   # 国际化状态管理
│   ├── chat.ts                   # 聊天状态管理
│   ├── knowledge.ts              # 知识库状态管理
│   ├── model.ts                  # 模型管理状态
│   ├── remote.ts                 # 远程配置状态
│   ├── network.ts                # 网络共享状态
│   ├── multimodal.ts             # 多模态处理状态
│   ├── plugin.ts                 # 插件系统状态
│   └── system.ts                 # 系统状态管理
├── views/                        # 页面视图 - 主导航页面
│   ├── Chat/                     # 聊天页面
│   │   ├── index.vue             # 聊天主页面
│   │   ├── SessionView.vue       # 会话视图页面
│   │   └── SettingsView.vue      # 聊天设置页面
│   ├── Knowledge/                # 知识库页面
│   │   ├── index.vue             # 知识库主页面
│   │   ├── DocumentView.vue      # 文档视图页面
│   │   ├── SearchView.vue        # 搜索视图页面
│   │   └── AnalyticsView.vue     # 分析视图页面
│   ├── Model/                    # 模型管理页面
│   │   ├── index.vue             # 模型管理主页面
│   │   ├── LocalModels.vue       # 本地模型页面
│   │   ├── OnlineModels.vue      # 在线模型页面
│   │   ├── DownloadCenter.vue    # 下载中心页面
│   │   └── PerformanceView.vue   # 性能监控页面
│   ├── Multimodal/               # 多模态页面
│   │   ├── index.vue             # 多模态主页面
│   │   ├── OCRView.vue           # OCR处理页面
│   │   ├── AudioView.vue         # 音频处理页面
│   │   ├── ImageView.vue         # 图像处理页面
│   │   └── VideoView.vue         # 视频处理页面
│   ├── Remote/                   # 远程配置页面
│   │   ├── index.vue             # 远程配置主页面
│   │   ├── ApiKeys.vue           # API密钥管理页面
│   │   ├── ProxyConfig.vue       # 代理配置页面
│   │   └── CloudSync.vue         # 云端同步页面
│   ├── Network/                  # 网络共享页面
│   │   ├── index.vue             # 网络共享主页面
│   │   ├── DeviceManager.vue     # 设备管理页面
│   │   ├── ResourceShare.vue     # 资源共享页面
│   │   └── TransferCenter.vue    # 传输中心页面
│   ├── Plugins/                  # 插件页面
│   │   ├── index.vue             # 插件主页面
│   │   ├── Market.vue            # 插件市场页面
│   │   ├── Installed.vue         # 已安装插件页面
│   │   └── Developer.vue         # 开发者页面
│   └── Settings/                 # 设置页面
│       ├── index.vue             # 设置主页面
│       ├── General.vue           # 通用设置页面
│       ├── Appearance.vue        # 外观设置页面
│       ├── Performance.vue       # 性能设置页面
│       └── Security.vue          # 安全设置页面
├── router/                       # 路由管理
│   ├── index.ts                  # 路由配置主文件
│   ├── routes.ts                 # 路由定义
│   ├── guards.ts                 # 路由守卫
│   └── modules/                  # 模块化路由
│       ├── chat.ts               # 聊天模块路由
│       ├── knowledge.ts          # 知识库模块路由
│       ├── model.ts              # 模型管理模块路由
│       ├── multimodal.ts         # 多模态模块路由
│       ├── remote.ts             # 远程配置模块路由
│       ├── network.ts            # 网络共享模块路由
│       ├── plugins.ts            # 插件模块路由
│       └── settings.ts           # 设置模块路由
├── utils/                        # 工具函数 - 通用工具和辅助函数
│   ├── index.ts                  # 工具函数统一导出
│   ├── request.ts                # HTTP请求工具
│   ├── websocket.ts              # WebSocket工具
│   ├── eventSource.ts            # SSE事件流工具
│   ├── storage.ts                # 本地存储工具
│   ├── crypto.ts                 # 加密解密工具
│   ├── file.ts                   # 文件处理工具
│   ├── format.ts                 # 格式化工具
│   ├── validation.ts             # 数据验证工具
│   ├── date.ts                   # 日期时间工具
│   ├── color.ts                  # 颜色处理工具
│   ├── device.ts                 # 设备信息工具
│   ├── performance.ts            # 性能监控工具
│   └── constants.ts              # 常量定义
├── types/                        # TypeScript类型定义
│   ├── index.ts                  # 类型定义统一导出
│   ├── api.ts                    # API接口类型定义
│   ├── chat.ts                   # 聊天相关类型定义
│   ├── knowledge.ts              # 知识库相关类型定义
│   ├── model.ts                  # 模型相关类型定义
│   ├── multimodal.ts             # 多模态相关类型定义
│   ├── remote.ts                 # 远程配置相关类型定义
│   ├── network.ts                # 网络共享相关类型定义
│   ├── plugin.ts                 # 插件相关类型定义
│   ├── system.ts                 # 系统相关类型定义
│   ├── user.ts                   # 用户相关类型定义
│   └── common.ts                 # 通用类型定义
├── locales/                      # 国际化文件
│   ├── index.ts                  # 国际化配置主文件
│   ├── zh-CN/                    # 中文语言包
│   │   ├── common.json           # 通用翻译
│   │   ├── chat.json             # 聊天模块翻译
│   │   ├── knowledge.json        # 知识库模块翻译
│   │   ├── model.json            # 模型管理模块翻译
│   │   ├── multimodal.json       # 多模态模块翻译
│   │   ├── remote.json           # 远程配置模块翻译
│   │   ├── network.json          # 网络共享模块翻译
│   │   ├── plugins.json          # 插件模块翻译
│   │   └── settings.json         # 设置模块翻译
│   └── en-US/                    # 英文语言包
│       ├── common.json           # 通用翻译
│       ├── chat.json             # 聊天模块翻译
│       ├── knowledge.json        # 知识库模块翻译
│       ├── model.json            # 模型管理模块翻译
│       ├── multimodal.json       # 多模态模块翻译
│       ├── remote.json           # 远程配置模块翻译
│       ├── network.json          # 网络共享模块翻译
│       ├── plugins.json          # 插件模块翻译
│       └── settings.json         # 设置模块翻译
├── directives/                   # Vue指令
│   ├── index.ts                  # 指令统一导出
│   ├── loading.ts                # 加载指令
│   ├── permission.ts             # 权限指令
│   ├── copy.ts                   # 复制指令
│   └── resize.ts                 # 尺寸变化指令
├── App.vue                       # 根组件
├── main.ts                       # 应用入口文件
└── env.d.ts                      # 环境变量类型定义
```

### 5.2 后端目录结构 (src-tauri/)

```
src-tauri/
├── src/                          # Rust源代码目录
│   ├── main.rs                   # 应用入口文件
│   ├── lib.rs                    # 库文件，定义公共模块
│   ├── ai/                       # AI核心模块 - 推理引擎、模型管理
│   │   ├── mod.rs                # AI模块定义
│   │   ├── inference.rs          # 推理引擎核心逻辑
│   │   ├── model.rs              # 模型抽象和管理
│   │   ├── tokenizer.rs          # 分词器管理
│   │   ├── memory.rs             # 内存管理和优化
│   │   ├── gpu.rs                # GPU资源管理
│   │   ├── quantization.rs       # 模型量化处理
│   │   ├── performance.rs        # 性能监控和优化
│   │   ├── deployment.rs         # 模型部署管理
│   │   ├── downloader.rs         # 模型下载器
│   │   ├── local_upload.rs       # 本地模型上传
│   │   ├── huggingface.rs        # HuggingFace集成
│   │   └── thinking.rs           # 思维链处理
│   ├── chat/                     # 聊天功能 - 会话管理、消息处理
│   │   ├── mod.rs                # 聊天模块定义
│   │   ├── session.rs            # 会话管理
│   │   ├── message.rs            # 消息处理
│   │   ├── history.rs            # 聊天历史管理
│   │   ├── context.rs            # 上下文管理
│   │   ├── streaming.rs          # 流式响应处理
│   │   └── export.rs             # 会话导出功能
│   ├── knowledge/                # 知识库 - 文档解析、向量搜索
│   │   ├── mod.rs                # 知识库模块定义
│   │   ├── document.rs           # 文档处理和解析
│   │   ├── vector.rs             # 向量数据库操作
│   │   ├── embedding.rs          # 文本向量化
│   │   ├── search.rs             # 语义搜索引擎
│   │   ├── chunking.rs           # 文档分块处理
│   │   ├── indexing.rs           # 索引管理
│   │   ├── rag.rs                # RAG增强检索
│   │   └── graph.rs              # 知识图谱构建
│   ├── multimodal/               # 多模态 - OCR、TTS、音频处理
│   │   ├── mod.rs                # 多模态模块定义
│   │   ├── ocr.rs                # OCR文字识别
│   │   ├── tts.rs                # 文字转语音
│   │   ├── asr.rs                # 语音转文字
│   │   ├── image.rs              # 图像处理和分析
│   │   ├── video.rs              # 视频处理和分析
│   │   ├── audio.rs              # 音频处理
│   │   ├── converter.rs          # 格式转换器
│   │   └── pipeline.rs           # 处理流水线
│   ├── remote/                   # 远程配置 - API密钥、云端集成
│   │   ├── mod.rs                # 远程配置模块定义
│   │   ├── config.rs             # 配置管理
│   │   ├── api_client.rs         # API客户端
│   │   ├── providers.rs          # 服务提供商集成
│   │   ├── proxy.rs              # 代理设置管理
│   │   ├── sync.rs               # 配置同步
│   │   └── validation.rs         # 配置验证
│   ├── network/                  # 网络共享 - P2P通信、文件传输
│   │   ├── mod.rs                # 网络模块定义
│   │   ├── discovery.rs          # 设备发现(mDNS)
│   │   ├── p2p.rs                # P2P通信协议
│   │   ├── transfer.rs           # 文件传输管理
│   │   ├── security.rs           # 网络安全和加密
│   │   ├── protocol.rs           # 通信协议定义
│   │   ├── node.rs               # 网络节点管理
│   │   └── sync.rs               # 数据同步机制
│   ├── plugins/                  # 插件系统 - WASM插件、市场
│   │   ├── mod.rs                # 插件模块定义
│   │   ├── runtime.rs            # 插件运行时
│   │   ├── sandbox.rs            # 沙箱环境
│   │   ├── market.rs             # 插件市场
│   │   ├── installer.rs          # 插件安装器
│   │   ├── manager.rs            # 插件管理器
│   │   ├── permissions.rs        # 权限管理
│   │   ├── api.rs                # 插件API接口
│   │   └── loader.rs             # 插件加载器
│   ├── system/                   # 系统管理 - 监控、配置、日志
│   │   ├── mod.rs                # 系统模块定义
│   │   ├── monitor.rs            # 系统监控
│   │   ├── config.rs             # 系统配置管理
│   │   ├── logger.rs             # 日志系统
│   │   ├── backup.rs             # 数据备份
│   │   ├── update.rs             # 自动更新
│   │   ├── health.rs             # 健康检查
│   │   ├── metrics.rs            # 性能指标
│   │   └── diagnostics.rs        # 系统诊断
│   ├── db/                       # 数据库 - SQLite和向量数据库
│   │   ├── mod.rs                # 数据库模块定义
│   │   ├── connection.rs         # 数据库连接管理
│   │   ├── migrations.rs         # 数据库迁移
│   │   ├── schema.rs             # 数据库模式定义
│   │   ├── models/               # 数据模型
│   │   │   ├── mod.rs            # 模型模块定义
│   │   │   ├── chat.rs           # 聊天相关模型
│   │   │   ├── knowledge.rs      # 知识库相关模型
│   │   │   ├── model.rs          # 模型管理相关模型
│   │   │   ├── network.rs        # 网络相关模型
│   │   │   ├── system.rs         # 系统相关模型
│   │   │   └── plugin.rs         # 插件相关模型
│   │   ├── repositories/         # 数据访问层
│   │   │   ├── mod.rs            # 仓储模块定义
│   │   │   ├── chat.rs           # 聊天数据访问
│   │   │   ├── knowledge.rs      # 知识库数据访问
│   │   │   ├── model.rs          # 模型数据访问
│   │   │   ├── network.rs        # 网络数据访问
│   │   │   ├── system.rs         # 系统数据访问
│   │   │   └── plugin.rs         # 插件数据访问
│   │   └── vector/               # 向量数据库
│   │       ├── mod.rs            # 向量数据库模块定义
│   │       ├── chroma.rs         # ChromaDB集成
│   │       ├── client.rs         # 向量数据库客户端
│   │       └── operations.rs     # 向量操作
│   ├── commands/                 # Tauri命令 - 前后端接口层
│   │   ├── mod.rs                # 命令模块定义
│   │   ├── chat.rs               # 聊天相关命令
│   │   ├── knowledge.rs          # 知识库相关命令
│   │   ├── model.rs              # 模型管理相关命令
│   │   ├── remote.rs             # 远程配置相关命令
│   │   ├── network.rs            # 网络共享相关命令
│   │   ├── multimodal.rs         # 多模态相关命令
│   │   ├── system.rs             # 系统管理相关命令
│   │   └── plugin.rs             # 插件系统相关命令
│   ├── events/                   # 事件系统 - 应用内事件通信
│   │   ├── mod.rs                # 事件模块定义
│   │   ├── chat.rs               # 聊天事件
│   │   ├── model.rs              # 模型事件
│   │   ├── system.rs             # 系统事件
│   │   ├── network.rs            # 网络事件
│   │   └── plugin.rs             # 插件事件
│   ├── utils/                    # 工具模块 - 通用工具和辅助函数
│   │   ├── mod.rs                # 工具模块定义
│   │   ├── crypto.rs             # 加密解密工具
│   │   ├── file.rs               # 文件操作工具
│   │   ├── network.rs            # 网络工具
│   │   ├── compression.rs        # 压缩解压工具
│   │   ├── validation.rs         # 数据验证工具
│   │   ├── serialization.rs      # 序列化工具
│   │   ├── time.rs               # 时间处理工具
│   │   ├── path.rs               # 路径处理工具
│   │   └── constants.rs          # 常量定义
│   ├── error/                    # 错误处理 - 统一错误管理
│   │   ├── mod.rs                # 错误模块定义
│   │   ├── types.rs              # 错误类型定义
│   │   ├── handler.rs            # 错误处理器
│   │   └── recovery.rs           # 错误恢复机制
│   └── config/                   # 配置管理 - 应用配置
│       ├── mod.rs                # 配置模块定义
│       ├── app.rs                # 应用配置
│       ├── database.rs           # 数据库配置
│       ├── ai.rs                 # AI配置
│       ├── network.rs            # 网络配置
│       └── security.rs           # 安全配置
├── migrations/                   # 数据库迁移文件
│   ├── 001_initial.sql           # 初始数据库结构
│   ├── 002_create_knowledge_tables.sql # 知识库相关表
│   ├── 003_create_config_tables.sql    # 配置相关表
│   ├── 004_create_network_tables.sql   # 网络相关表
│   ├── 005_create_plugin_tables.sql    # 插件相关表
│   └── 006_create_indexes.sql          # 索引创建
├── capabilities/                 # Tauri权限配置
│   └── default.json              # 默认权限配置
├── icons/                        # 应用图标
│   ├── 32x32.png                 # 32x32图标
│   ├── 128x128.png               # 128x128图标
│   ├── icon.icns                 # macOS图标
│   └── icon.ico                  # Windows图标
├── Cargo.toml                    # Rust项目配置文件
├── tauri.conf.json               # Tauri配置文件
└── build.rs                      # 构建脚本
```

## 6. 界面设计

### 6.1 整体UI设计规范

**设计原则**
- **简洁明了**：界面布局清晰，功能分区明确
- **一致性**：统一的设计语言和交互模式
- **响应式**：适配不同屏幕尺寸和分辨率
- **可访问性**：支持键盘导航和屏幕阅读器

**色彩系统**
- **主色调**：蓝色系 (#1890ff)，体现科技感和专业性
- **辅助色**：灰色系，用于文本和边框
- **状态色**：成功(绿色)、警告(橙色)、错误(红色)、信息(蓝色)
- **主题支持**：浅色主题和深色主题

**字体系统**
- **主字体**：系统默认字体 (SF Pro Display / Microsoft YaHei)
- **代码字体**：等宽字体 (JetBrains Mono / Consolas)
- **字体大小**：12px(小)、14px(正常)、16px(大)、18px(标题)

### 6.2 主界面布局设计

**整体布局**
采用经典的桌面应用布局，包含顶部导航栏、主内容区域和状态栏：

```
┌─────────────────────────────────────────────────────────────┐
│  Logo    聊天  知识库  模型  多模态  远程  网络  插件  设置   │ 顶部导航
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                     主内容区域                              │
│                                                             │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│  状态信息                                        系统状态    │ 底部状态栏
└─────────────────────────────────────────────────────────────┘
```

**顶部导航栏**
- **左侧Logo区域**：显示AI Studio应用Logo和名称
- **中央导航区域**：8个主导航标签页
  - 聊天 (Chat) - 图标：💬
  - 知识库 (Knowledge) - 图标：📚
  - 模型管理 (Model) - 图标：🤖
  - 多模态 (Multimodal) - 图标：🎭
  - 远程配置 (Remote) - 图标：🌐
  - 网络共享 (Network) - 图标：🔗
  - 插件系统 (Plugins) - 图标：🧩
  - 设置 (Settings) - 图标：⚙️
- **右侧用户区域**：用户头像和下拉菜单
  - 主题切换 (Theme) - 图标：🌓
  - 语言切换 (Language) - 图标：🌍
  - 用户信息 (Profile) - 图标：👤

**主题设计**
- **浅色主题**：白色背景，深色文字，蓝色主色调
- **深色主题**：深灰色背景，浅色文字，蓝色主色调
- **主题切换**：平滑过渡动画，保持用户体验一致性

### 6.3 聊天模块界面设计

**布局结构**：左侧会话列表 + 右侧聊天区域

**左侧会话列表 (300px宽度)**
- **顶部操作栏**：
  - 新建会话按钮 - 点击创建新的聊天会话
  - 搜索框 - 搜索历史会话
  - 排序选项 - 按时间/名称排序
- **会话列表**：
  - 会话项显示：会话标题、最后消息预览、时间戳
  - 右键菜单：重命名、删除、导出、置顶
  - 拖拽排序：支持会话拖拽重新排序
- **底部统计**：显示总会话数、今日消息数

**右侧聊天区域**
- **顶部工具栏**：
  - 会话标题（可编辑）
  - 模型选择下拉框
  - 参数设置按钮（温度、最大token等）
  - 清空会话按钮
  - 导出会话按钮
- **消息显示区域**：
  - 用户消息：右对齐，蓝色气泡
  - AI回复：左对齐，灰色气泡
  - 系统消息：居中显示，小字体
  - Markdown渲染：支持代码高亮、表格、公式
  - 附件显示：图片预览、文件下载链接
- **输入区域**：
  - 多行文本输入框（支持Markdown）
  - 附件上传按钮（图片、文档、音频）
  - 发送按钮（Ctrl+Enter快捷键）
  - 语音输入按钮
  - 表情符号按钮

**交互逻辑**
- **消息发送**：点击发送或快捷键触发
- **流式显示**：AI回复逐字显示，带打字机效果
- **消息操作**：悬停显示复制、重新生成、删除按钮
- **会话管理**：支持会话重命名、删除、导出
- **快捷操作**：支持键盘快捷键操作

### 6.4 知识库模块界面设计

**布局结构**：左侧知识库列表 + 右侧文档管理区域

**左侧知识库列表 (280px宽度)**
- **顶部操作栏**：
  - 创建知识库按钮
  - 导入知识库按钮
  - 搜索框
- **知识库列表**：
  - 知识库项显示：名称、文档数量、大小、创建时间
  - 右键菜单：编辑、删除、导出、备份
  - 状态指示：处理中、已完成、错误状态

**右侧文档管理区域**
- **顶部工具栏**：
  - 知识库名称和描述
  - 上传文档按钮
  - 批量操作按钮
  - 搜索框
  - 视图切换（列表/网格）
- **文档列表**：
  - 文档项显示：文件名、类型、大小、状态、处理时间
  - 状态指示：处理中、已完成、失败
  - 进度条：显示处理进度
  - 操作按钮：预览、下载、删除、重新处理
- **搜索结果区域**：
  - 语义搜索结果列表
  - 相关度评分显示
  - 高亮匹配文本
  - 来源文档链接

**交互逻辑**
- **文档上传**：拖拽上传或点击选择文件
- **批量操作**：支持多选文档进行批量删除/处理
- **实时搜索**：输入关键词实时显示搜索结果
- **文档预览**：支持PDF、Word、图片等格式预览

### 6.5 模型管理模块界面设计

**布局结构**：顶部标签页 + 主内容区域

**标签页导航**
- 本地模型 - 显示已下载的模型
- 在线模型 - 浏览和搜索在线模型
- 下载中心 - 管理下载任务
- 性能监控 - 模型性能统计

**本地模型页面**
- **模型列表**：
  - 模型卡片显示：名称、版本、大小、状态
  - 状态指示：未加载、已加载、加载中、错误
  - 操作按钮：加载、卸载、配置、删除、量化
- **模型详情面板**：
  - 模型信息：架构、参数量、上下文长度
  - 配置选项：量化级别、GPU层数、内存限制
  - 性能数据：推理速度、内存使用、GPU利用率

**在线模型页面**
- **搜索和筛选**：
  - 搜索框：按名称、作者、标签搜索
  - 筛选器：模型类型、大小、许可证
  - 排序选项：下载量、评分、更新时间
- **模型列表**：
  - 模型卡片：名称、作者、描述、评分、大小
  - 下载按钮：一键下载模型
  - 详情链接：查看模型详细信息

**下载中心页面**
- **下载任务列表**：
  - 任务信息：模型名称、文件大小、下载进度
  - 状态显示：等待中、下载中、已完成、失败、暂停
  - 操作按钮：暂停、恢复、取消、重试
- **下载统计**：
  - 总下载量、当前速度、剩余时间
  - 网络状态和连接质量

### 6.6 多模态模块界面设计

**布局结构**：功能标签页 + 处理区域

**功能标签页**
- OCR识别 - 图片文字识别
- 语音处理 - TTS和ASR功能
- 图像分析 - 图像理解和描述
- 视频处理 - 视频分析和字幕

**处理区域**
- **文件上传区域**：拖拽上传或选择文件
- **参数设置**：处理参数和选项配置
- **处理结果**：显示处理结果和输出文件
- **任务队列**：批量处理任务管理

### 6.7 远程配置模块界面设计

**布局结构**：左侧配置列表 + 右侧配置详情

**左侧配置列表 (300px宽度)**
- **配置项显示**：服务商名称、状态、最后测试时间
- **状态指示**：已连接、未连接、测试中、错误
- **操作按钮**：新增、编辑、删除、测试连接

**右侧配置详情**
- **基本信息**：配置名称、服务商类型、描述
- **API配置**：API密钥、基础URL、模型名称
- **参数设置**：最大token、温度、top_p等
- **代理设置**：代理类型、地址、端口、认证
- **测试结果**：连接状态、响应时间、错误信息

### 6.8 网络共享模块界面设计

**布局结构**：设备发现 + 资源共享 + 传输管理

**设备发现区域**
- **在线设备列表**：设备名称、IP地址、状态、能力
- **连接操作**：连接、断开、信任设备
- **设备详情**：系统信息、共享资源、权限设置

**资源共享区域**
- **本地资源**：可共享的模型、知识库、配置
- **远程资源**：其他设备共享的资源
- **权限管理**：设置访问权限和安全策略

**传输管理区域**
- **传输任务**：文件名、大小、进度、速度、状态
- **传输控制**：暂停、恢复、取消传输
- **传输历史**：已完成的传输记录

### 6.9 插件系统界面设计

**布局结构**：标签页导航 + 插件管理区域

**标签页导航**
- 已安装 - 显示已安装的插件
- 插件市场 - 浏览和安装新插件
- 开发者 - 插件开发工具

**已安装页面**
- **插件列表**：插件名称、版本、状态、操作按钮
- **插件详情**：描述、权限、配置选项
- **插件控制**：启用、禁用、卸载、配置

**插件市场页面**
- **搜索和筛选**：按类别、评分、下载量筛选
- **插件卡片**：名称、描述、评分、截图
- **安装操作**：一键安装、查看详情

### 6.10 设置模块界面设计

**布局结构**：左侧设置分类 + 右侧设置内容

**设置分类**
- 通用设置 - 基本应用设置
- 外观设置 - 主题和界面设置
- 性能设置 - 性能优化选项
- 安全设置 - 安全和隐私设置
- 备份设置 - 数据备份配置
- 更新设置 - 自动更新配置

**设置内容**
- **表单控件**：开关、滑块、下拉框、输入框
- **实时预览**：设置变更的实时效果预览
- **重置选项**：恢复默认设置功能

## 7. API接口设计

### 7.1 接口设计规范

**RESTful API设计原则**
- **统一资源标识符**：使用清晰的URL路径结构
- **HTTP方法语义**：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- **状态码规范**：200(成功)、201(创建)、400(请求错误)、401(未授权)、404(未找到)、500(服务器错误)
- **响应格式统一**：JSON格式，包含data、message、code字段

**接口响应格式**
```typescript
interface ApiResponse<T> {
  code: number;           // 状态码
  message: string;        // 响应消息
  data?: T;              // 响应数据
  timestamp: number;      // 时间戳
  request_id: string;     // 请求ID
}
```

**分页响应格式**
```typescript
interface PaginatedResponse<T> {
  items: T[];            // 数据列表
  total: number;         // 总数量
  page: number;          // 当前页码
  limit: number;         // 每页数量
  has_more: boolean;     // 是否有更多数据
}
```

### 7.2 聊天模块API接口

**发送消息接口**
```typescript
POST /api/chat/send
Request: {
  session_id: string;
  message: string;
  attachments?: Array<{
    type: 'image' | 'document' | 'audio';
    url: string;
    name: string;
    size: number;
  }>;
  model_config?: {
    model_id: string;
    temperature: number;
    max_tokens: number;
    top_p: number;
  };
}
Response: {
  message_id: string;
  status: 'success' | 'error';
  error?: string;
}
```

**SSE流式响应接口**
```typescript
GET /api/chat/stream/{message_id}
Response: Server-Sent Events
data: {
  type: 'token' | 'done' | 'error';
  content: string;
  metadata?: {
    tokens_used: number;
    response_time: number;
  };
}
```

**会话管理接口**
```typescript
// 获取会话列表
GET /api/chat/sessions
Query: {
  page?: number;
  limit?: number;
  search?: string;
  sort?: 'created_at' | 'updated_at' | 'title';
  order?: 'asc' | 'desc';
}

// 创建会话
POST /api/chat/sessions
Request: {
  title?: string;
  model_id?: string;
  system_prompt?: string;
  config?: {
    temperature: number;
    max_tokens: number;
    top_p: number;
  };
}

// 更新会话
PUT /api/chat/sessions/{id}
Request: {
  title?: string;
  system_prompt?: string;
  config?: object;
}

// 删除会话
DELETE /api/chat/sessions/{id}

// 获取会话消息
GET /api/chat/messages/{session_id}
Query: {
  page?: number;
  limit?: number;
  before?: string;
  after?: string;
}
```

### 7.3 知识库模块API接口

**知识库管理接口**
```typescript
// 创建知识库
POST /api/knowledge
Request: {
  name: string;
  description?: string;
  embedding_model?: string;
  chunk_size?: number;
  chunk_overlap?: number;
}

// 获取知识库列表
GET /api/knowledge
Query: {
  page?: number;
  limit?: number;
  search?: string;
}

// 更新知识库
PUT /api/knowledge/{id}
Request: {
  name?: string;
  description?: string;
  embedding_model?: string;
  chunk_size?: number;
  chunk_overlap?: number;
}

// 删除知识库
DELETE /api/knowledge/{id}
```

**文档管理接口**
```typescript
// 上传文档
POST /api/knowledge/{kb_id}/upload
Request: FormData {
  files: File[];
  auto_process?: boolean;
}
Response: {
  documents: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    status: 'processing' | 'completed' | 'failed';
  }>;
}

// 获取文档列表
GET /api/knowledge/{kb_id}/documents
Query: {
  page?: number;
  limit?: number;
  status?: string;
  type?: string;
}

// 删除文档
DELETE /api/knowledge/{kb_id}/documents/{doc_id}
```

**语义搜索接口**
```typescript
POST /api/knowledge/{kb_id}/search
Request: {
  query: string;
  limit?: number;
  threshold?: number;
  filters?: {
    document_types?: string[];
    date_range?: {
      start: string;
      end: string;
    };
  };
}
Response: {
  results: Array<{
    document_id: string;
    document_name: string;
    chunk_id: string;
    content: string;
    score: number;
    metadata: {
      page_number?: number;
      chunk_index: number;
    };
  }>;
  total: number;
  query_time: number;
}
```

### 7.4 模型管理模块API接口

**模型管理接口**
```typescript
// 获取模型列表
GET /api/models
Query: {
  type?: 'local' | 'online' | 'all';
  category?: string;
  search?: string;
  page?: number;
  limit?: number;
}

// 搜索在线模型
GET /api/models/search
Query: {
  query: string;
  category?: string;
  min_size?: number;
  max_size?: number;
  quantization?: string;
  sort?: 'downloads' | 'rating' | 'updated';
  page?: number;
  limit?: number;
}

// 下载模型
POST /api/models/download
Request: {
  model_id?: string;
  huggingface_id?: string;
  download_url?: string;
  quantization?: string;
  mirror?: 'huggingface' | 'hf-mirror';
}
Response: {
  task_id: string;
  model_id: string;
  status: 'pending' | 'downloading';
}

// 加载模型
POST /api/models/{model_id}/load
Request: {
  config?: {
    gpu_layers?: number;
    context_length?: number;
    batch_size?: number;
    threads?: number;
  };
}
Response: {
  status: 'success' | 'error';
  message?: string;
  memory_usage?: number;
  load_time?: number;
}

// 卸载模型
POST /api/models/{model_id}/unload

// 删除模型
DELETE /api/models/{id}

// 模型量化
POST /api/models/{model_id}/quantize
Request: {
  quantization_type: 'q4_0' | 'q4_1' | 'q8_0' | 'q8_1';
  output_path?: string;
}
Response: {
  task_id: string;
  status: 'pending' | 'processing';
  estimated_time?: number;
}
```

### 7.5 多模态处理模块API接口

**OCR识别接口**
```typescript
POST /api/multimodal/ocr
Request: FormData {
  image: File;
  language?: string;
  output_format?: 'text' | 'json' | 'pdf';
}
Response: {
  task_id: string;
  text: string;
  confidence: number;
  bounding_boxes?: Array<{
    text: string;
    x: number;
    y: number;
    width: number;
    height: number;
    confidence: number;
  }>;
  processing_time: number;
}
```

**语音处理接口**
```typescript
// 文字转语音
POST /api/multimodal/tts
Request: {
  text: string;
  voice?: string;
  speed?: number;
  pitch?: number;
  output_format?: 'wav' | 'mp3' | 'ogg';
}
Response: {
  task_id: string;
  audio_url: string;
  duration: number;
  file_size: number;
}

// 语音转文字
POST /api/multimodal/asr
Request: FormData {
  audio: File;
  language?: string;
  model?: string;
}
Response: {
  task_id: string;
  text: string;
  confidence: number;
  segments?: Array<{
    text: string;
    start: number;
    end: number;
    confidence: number;
  }>;
  processing_time: number;
}
```

**图像和视频处理接口**
```typescript
// 图像分析
POST /api/multimodal/image/analyze
Request: FormData {
  image: File;
  analysis_type: 'description' | 'objects' | 'text' | 'faces';
}

// 视频分析
POST /api/multimodal/video/analyze
Request: FormData {
  video: File;
  analysis_type: 'summary' | 'subtitles' | 'objects';
}

// 格式转换
POST /api/multimodal/convert
Request: FormData {
  file: File;
  target_format: string;
  quality?: number;
}
```

**任务管理接口**
```typescript
// 获取任务列表
GET /api/multimodal/tasks
Query: {
  status?: string;
  type?: string;
  page?: number;
  limit?: number;
}

// 获取任务详情
GET /api/multimodal/tasks/{id}

// 删除任务
DELETE /api/multimodal/tasks/{id}

// 获取处理历史
GET /api/multimodal/history
Query: {
  page?: number;
  limit?: number;
  date_range?: {
    start: string;
    end: string;
  };
}
```

### 7.6 远程配置模块API接口

**配置管理接口**
```typescript
// 获取配置列表
GET /api/remote/configs
Response: {
  configs: Array<{
    id: string;
    name: string;
    provider: string;
    model_name: string;
    is_active: boolean;
    last_test_at?: string;
    status: 'connected' | 'disconnected' | 'error';
    created_at: string;
  }>;
}

// 创建配置
POST /api/remote/configs
Request: {
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'custom';
  api_key: string;
  base_url?: string;
  model_name: string;
  max_tokens?: number;
  temperature?: number;
  proxy?: {
    type: 'http' | 'https' | 'socks5';
    host: string;
    port: number;
    username?: string;
    password?: string;
  };
}

// 更新配置
PUT /api/remote/configs/{id}

// 删除配置
DELETE /api/remote/configs/{id}

// 测试配置
POST /api/remote/configs/{id}/test
Response: {
  status: 'success' | 'error';
  response_time?: number;
  error_message?: string;
  model_info?: {
    name: string;
    context_length: number;
    capabilities: string[];
  };
}

// 激活配置
POST /api/remote/configs/{id}/activate
```

### 7.7 网络共享模块API接口

**设备管理接口**
```typescript
// 设备发现
GET /api/network/discover
Response: {
  devices: Array<{
    id: string;
    name: string;
    device_type: string;
    ip_address: string;
    port: number;
    capabilities: string[];
    status: 'online' | 'offline' | 'busy';
    last_seen: string;
  }>;
}

// 连接设备
POST /api/network/connect
Request: {
  device_id: string;
  password?: string;
}
Response: {
  status: 'success' | 'error';
  connection_id?: string;
  error_message?: string;
}

// 断开连接
POST /api/network/disconnect
Request: {
  device_id: string;
}

// 获取节点列表
GET /api/network/nodes
```

**资源共享接口**
```typescript
// 共享资源
POST /api/network/share
Request: {
  resource_type: 'model' | 'knowledge_base' | 'config';
  resource_id: string;
  permissions: {
    read: boolean;
    write: boolean;
    download: boolean;
  };
  is_public: boolean;
  allowed_devices?: string[];
}
Response: {
  share_id: string;
  share_url: string;
  qr_code?: string;
}

// 获取共享资源列表
GET /api/network/resources
Query: {
  resource_type?: string;
  node_id?: string;
}
```

**传输管理接口**
```typescript
// 开始传输
POST /api/network/transfer
Request: {
  source_node_id: string;
  target_node_id: string;
  resource_type: string;
  resource_id: string;
}

// 获取传输任务列表
GET /api/network/transfers
Query: {
  status?: string;
  page?: number;
  limit?: number;
}

// 暂停传输
POST /api/network/transfers/{id}/pause

// 恢复传输
POST /api/network/transfers/{id}/resume

// 取消传输
DELETE /api/network/transfers/{id}
```

### 7.8 插件系统API接口

**插件管理接口**
```typescript
// 获取已安装插件列表
GET /api/plugins
Response: {
  plugins: Array<{
    id: string;
    name: string;
    display_name: string;
    version: string;
    author: string;
    category: string;
    status: 'enabled' | 'disabled' | 'error';
    permissions: string[];
    created_at: string;
  }>;
}

// 安装插件
POST /api/plugins/install
Request: {
  source: 'market' | 'local' | 'url';
  plugin_id?: string;
  file_path?: string;
  download_url?: string;
}
Response: {
  plugin_id: string;
  status: 'installing' | 'installed' | 'error';
  message?: string;
}

// 卸载插件
DELETE /api/plugins/{id}

// 启用插件
POST /api/plugins/{id}/enable

// 禁用插件
POST /api/plugins/{id}/disable

// 获取插件配置
GET /api/plugins/{id}/config

// 更新插件配置
PUT /api/plugins/{id}/config
Request: {
  config: Record<string, any>;
}

// 执行插件功能
POST /api/plugins/{id}/execute
Request: {
  function_name: string;
  parameters: Record<string, any>;
  context?: {
    user_id?: string;
    session_id?: string;
  };
}
Response: {
  result: any;
  execution_time: number;
  status: 'success' | 'error';
  error_message?: string;
}
```

**插件市场接口**
```typescript
// 获取插件市场列表
GET /api/plugins/market
Query: {
  category?: string;
  search?: string;
  sort?: 'downloads' | 'rating' | 'updated';
  page?: number;
  limit?: number;
}
Response: {
  plugins: Array<{
    id: string;
    name: string;
    description: string;
    version: string;
    author: string;
    category: string;
    rating: number;
    download_count: number;
    file_size: number;
    screenshots: string[];
    is_verified: boolean;
    updated_at: string;
  }>;
  total: number;
}

// 搜索插件
GET /api/plugins/market/search
Query: {
  query: string;
  category?: string;
}

// 获取插件详情
GET /api/plugins/market/{id}

// 从市场安装插件
POST /api/plugins/market/{id}/install

// 获取插件执行日志
GET /api/plugins/{id}/logs
Query: {
  level?: string;
  page?: number;
  limit?: number;
}
```

### 7.9 系统管理API接口

**系统信息接口**
```typescript
// 获取系统信息
GET /api/system/info
Response: {
  system: {
    os: string;
    arch: string;
    version: string;
    memory_total: number;
    memory_available: number;
    cpu_cores: number;
    gpu_info?: {
      name: string;
      memory: number;
      driver_version: string;
    };
  };
  application: {
    version: string;
    build_date: string;
    commit_hash: string;
  };
}

// 获取性能监控数据
GET /api/system/performance
Query: {
  duration?: number; // 时间范围(秒)
  interval?: number; // 采样间隔(秒)
}
Response: {
  metrics: Array<{
    timestamp: number;
    cpu_usage: number;
    memory_usage: number;
    gpu_usage?: number;
    disk_usage: number;
    network_io: {
      bytes_sent: number;
      bytes_received: number;
    };
  }>;
}

// 健康检查
GET /api/system/health
Response: {
  status: 'healthy' | 'warning' | 'error';
  checks: {
    database: 'ok' | 'error';
    vector_db: 'ok' | 'error';
    ai_engine: 'ok' | 'error';
    network: 'ok' | 'error';
  };
  uptime: number;
  last_check: string;
}
```

**配置管理接口**
```typescript
// 获取系统配置
GET /api/system/configs
Response: {
  configs: Record<string, {
    value: any;
    type: string;
    description: string;
    is_user_configurable: boolean;
  }>;
}

// 更新系统配置
PUT /api/system/configs
Request: {
  configs: Record<string, any>;
}
Response: {
  updated_configs: string[];
  restart_required: boolean;
}
```

**日志管理接口**
```typescript
// 查询系统日志
GET /api/system/logs
Query: {
  level?: 'debug' | 'info' | 'warn' | 'error';
  module?: string;
  start_time?: string;
  end_time?: string;
  search?: string;
  page?: number;
  limit?: number;
}
Response: {
  logs: Array<{
    id: number;
    level: string;
    module: string;
    message: string;
    metadata?: object;
    created_at: string;
  }>;
  total: number;
}
```

**备份管理接口**
```typescript
// 创建备份
POST /api/system/backup
Request: {
  backup_type: 'full' | 'incremental';
  include_models?: boolean;
  include_knowledge?: boolean;
  include_configs?: boolean;
}
Response: {
  backup_id: string;
  status: 'started' | 'error';
  estimated_size?: number;
}

// 获取备份列表
GET /api/system/backups
Query: {
  page?: number;
  limit?: number;
}
Response: {
  backups: Array<{
    id: string;
    backup_type: string;
    file_path: string;
    file_size: number;
    checksum: string;
    status: 'completed' | 'failed';
    created_at: string;
  }>;
  total: number;
}

// 恢复备份
POST /api/system/restore
Request: {
  backup_id: string;
  restore_options: {
    restore_models: boolean;
    restore_knowledge: boolean;
    restore_configs: boolean;
  };
}
Response: {
  status: 'started' | 'error';
  message?: string;
}
```

**更新管理接口**
```typescript
// 检查更新
POST /api/system/update/check
Response: {
  has_update: boolean;
  current_version: string;
  latest_version?: string;
  release_notes?: string;
  download_size?: number;
  release_date?: string;
}

// 安装更新
POST /api/system/update/install
Request: {
  version?: string;
  auto_restart?: boolean;
}
Response: {
  status: 'started' | 'error';
  message?: string;
  restart_required?: boolean;
}
```

## 8. 业务流程设计

### 8.1 应用启动和初始化流程

```
用户启动应用
    ↓
检查系统环境
    ↓
初始化数据库连接
    ↓
加载系统配置
    ↓
初始化AI推理引擎
    ↓
启动向量数据库服务
    ↓
加载用户偏好设置
    ↓
初始化主题和语言
    ↓
显示主界面
    ↓
检查模型状态
    ↓
启动后台服务
    ↓
应用就绪
```

### 8.2 聊天对话处理流程

```
用户输入消息
    ↓
验证输入内容
    ↓
检查会话状态
    ↓
处理附件（如有）
    ↓
构建消息上下文
    ↓
选择推理引擎
    ↓
[本地模型分支]          [远程API分支]
检查模型加载状态         验证API配置
    ↓                      ↓
加载模型（如需要）       构建API请求
    ↓                      ↓
本地推理处理             发送远程请求
    ↓                      ↓
流式输出响应             处理API响应
    ↓                      ↓
        合并到主流程
            ↓
    保存消息到数据库
            ↓
    更新会话状态
            ↓
    触发前端更新
            ↓
    处理完成
```

### 8.3 知识库文档处理流程

```
用户上传文档
    ↓
文件格式检测
    ↓
[PDF分支]     [Word分支]    [Excel分支]    [Markdown分支]
PDF解析器     DOCX解析器    表格解析器     MD解析器
    ↓             ↓            ↓             ↓
提取文本和图片  提取文本和格式  提取数据       提取结构化内容
    ↓             ↓            ↓             ↓
        合并到主流程
            ↓
    文本预处理和清洗
            ↓
    智能分块处理
            ↓
    生成文本向量
            ↓
    存储到向量数据库
            ↓
    更新文档索引
            ↓
    更新处理状态
            ↓
    通知前端完成
```

### 8.4 模型下载和管理流程

```
用户搜索模型
    ↓
查询HuggingFace API
    ↓
显示搜索结果
    ↓
用户选择下载
    ↓
检查本地存储空间
    ↓
选择下载源
    ↓
[官方源分支]        [镜像源分支]
连接HF官方服务器    连接镜像服务器
    ↓                  ↓
        合并到主流程
            ↓
    创建下载任务
            ↓
    分片下载文件
            ↓
    实时更新进度
            ↓
    验证文件完整性
            ↓
    解压和安装模型
            ↓
    更新模型数据库
            ↓
    通知下载完成
```

### 8.5 多模态处理流程

```
用户上传媒体文件
    ↓
文件类型检测
    ↓
[图片分支]    [音频分支]    [视频分支]
图片处理      音频处理      视频处理
    ↓            ↓            ↓
OCR识别       ASR转换       帧提取
图像分析      TTS合成       字幕生成
    ↓            ↓            ↓
        合并到主流程
            ↓
    创建处理任务
            ↓
    加入任务队列
            ↓
    异步处理执行
            ↓
    实时更新进度
            ↓
    生成处理结果
            ↓
    保存输出文件
            ↓
    更新任务状态
            ↓
    通知处理完成
```

### 8.6 网络设备发现和连接流程

```
启动设备发现
    ↓
广播mDNS查询
    ↓
接收设备响应
    ↓
解析设备信息
    ↓
验证设备能力
    ↓
显示可用设备
    ↓
用户选择连接
    ↓
发起连接请求
    ↓
设备身份验证
    ↓
建立安全通道
    ↓
交换公钥信息
    ↓
协商通信协议
    ↓
建立P2P连接
    ↓
同步资源列表
    ↓
连接建立完成
```

### 8.7 插件安装和执行流程

```
用户浏览插件市场
    ↓
搜索和筛选插件
    ↓
查看插件详情
    ↓
用户选择安装
    ↓
下载插件文件
    ↓
验证插件签名
    ↓
检查权限要求
    ↓
用户确认权限
    ↓
安装到沙箱环境
    ↓
初始化插件运行时
    ↓
注册插件API
    ↓
加载插件配置
    ↓
启用插件功能
    ↓
插件安装完成
    ↓
[执行插件功能]
用户调用插件
    ↓
验证执行权限
    ↓
准备执行环境
    ↓
调用插件函数
    ↓
监控执行过程
    ↓
收集执行结果
    ↓
清理执行环境
    ↓
返回执行结果
```

## 9. 部署与运维

### 9.1 部署方案

**开发环境部署**
```bash
# 1. 克隆项目
git clone https://github.com/your-org/ai-studio.git
cd ai-studio

# 2. 安装前端依赖
npm install

# 3. 安装Rust工具链
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustup update

# 4. 安装Tauri CLI
cargo install tauri-cli

# 5. 启动开发服务器
npm run tauri dev
```

**生产环境构建**
```bash
# 1. 构建前端资源
npm run build

# 2. 构建Tauri应用
npm run tauri build

# 3. 生成安装包
# Windows: .msi 和 .exe 安装包
# macOS: .dmg 和 .app 应用包
# Linux: .deb 和 .AppImage 包
```

**系统要求**
- **操作系统**：Windows 10+ / macOS 10.15+ / Ubuntu 18.04+
- **内存**：最低 4GB，推荐 8GB+
- **存储**：最低 10GB 可用空间
- **GPU**：可选，支持CUDA 11.0+或Metal
- **网络**：可选，用于模型下载和远程API

### 9.2 配置管理

**应用配置文件**
```json
{
  "app": {
    "name": "AI Studio",
    "version": "1.0.0",
    "data_dir": "~/.ai-studio",
    "log_level": "info",
    "max_log_files": 10
  },
  "database": {
    "sqlite_path": "data/app.db",
    "connection_pool_size": 10,
    "backup_interval": 3600
  },
  "ai": {
    "default_model": "llama2-7b-chat",
    "max_context_length": 4096,
    "inference_timeout": 300,
    "gpu_enabled": true,
    "gpu_layers": -1
  },
  "vector_db": {
    "chroma_host": "localhost",
    "chroma_port": 8000,
    "collection_prefix": "ai_studio",
    "embedding_model": "sentence-transformers/all-MiniLM-L6-v2"
  },
  "network": {
    "mdns_enabled": true,
    "p2p_port": 8080,
    "max_connections": 10,
    "encryption_enabled": true
  },
  "security": {
    "api_key_encryption": true,
    "session_timeout": 86400,
    "max_file_size": 104857600,
    "allowed_file_types": ["pdf", "docx", "txt", "md", "jpg", "png"]
  }
}
```

**环境变量配置**
```bash
# 数据目录
AI_STUDIO_DATA_DIR=/path/to/data

# 日志级别
AI_STUDIO_LOG_LEVEL=info

# GPU配置
AI_STUDIO_GPU_ENABLED=true
CUDA_VISIBLE_DEVICES=0

# 网络配置
AI_STUDIO_P2P_PORT=8080
AI_STUDIO_MDNS_ENABLED=true

# 安全配置
AI_STUDIO_ENCRYPTION_KEY=your-encryption-key
AI_STUDIO_SESSION_SECRET=your-session-secret
```

### 9.3 监控与维护

**性能监控指标**
- **系统资源**：CPU使用率、内存使用率、磁盘使用率
- **AI推理**：推理速度、模型加载时间、GPU利用率
- **数据库**：查询响应时间、连接池状态、存储使用量
- **网络**：连接数量、传输速度、错误率
- **用户行为**：活跃用户数、会话数量、功能使用统计

**日志管理**
```rust
// 日志配置示例
use tracing::{info, warn, error};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

fn init_logging() {
    tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new("ai_studio=info"))
        .with(tracing_subscriber::fmt::layer())
        .with(tracing_appender::rolling::daily("logs", "ai-studio.log"))
        .init();
}

// 使用示例
info!("Application started successfully");
warn!("Model loading took longer than expected: {}ms", duration);
error!("Failed to connect to vector database: {}", error);
```

**健康检查**
```typescript
// 健康检查端点
interface HealthCheck {
  status: 'healthy' | 'warning' | 'error';
  timestamp: string;
  checks: {
    database: CheckResult;
    vector_db: CheckResult;
    ai_engine: CheckResult;
    disk_space: CheckResult;
    memory_usage: CheckResult;
  };
  uptime: number;
}

interface CheckResult {
  status: 'ok' | 'warning' | 'error';
  message?: string;
  value?: number;
  threshold?: number;
}
```

**自动备份策略**
- **增量备份**：每小时备份变更数据
- **全量备份**：每日备份完整数据库
- **模型备份**：定期备份重要模型文件
- **配置备份**：备份用户配置和设置
- **备份保留**：保留最近30天的备份文件

### 9.4 故障排除

**常见问题及解决方案**

1. **模型加载失败**
   - 检查模型文件完整性
   - 验证内存是否充足
   - 确认GPU驱动版本
   - 查看错误日志详情

2. **向量数据库连接失败**
   - 检查ChromaDB服务状态
   - 验证网络连接
   - 确认端口是否被占用
   - 重启向量数据库服务

3. **文档处理失败**
   - 检查文件格式支持
   - 验证文件大小限制
   - 确认磁盘空间充足
   - 查看处理日志

4. **网络共享问题**
   - 检查防火墙设置
   - 验证mDNS服务
   - 确认网络权限
   - 重置网络配置

**诊断工具**
```bash
# 系统诊断命令
ai-studio --diagnose

# 检查模型状态
ai-studio --check-models

# 验证数据库
ai-studio --verify-database

# 网络连接测试
ai-studio --test-network

# 清理缓存
ai-studio --clean-cache
```

### 9.5 测试策略

**单元测试**
```typescript
// 前端单元测试示例 (Vitest)
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import ChatMessage from '@/components/chat/ChatMessage.vue'

describe('ChatMessage', () => {
  it('renders user message correctly', () => {
    const wrapper = mount(ChatMessage, {
      props: {
        message: {
          role: 'user',
          content: 'Hello, AI!',
          timestamp: new Date().toISOString()
        }
      }
    })

    expect(wrapper.text()).toContain('Hello, AI!')
    expect(wrapper.classes()).toContain('user-message')
  })

  it('renders markdown content', () => {
    const wrapper = mount(ChatMessage, {
      props: {
        message: {
          role: 'assistant',
          content: '```python\nprint("Hello")\n```',
          timestamp: new Date().toISOString()
        }
      }
    })

    expect(wrapper.find('code').exists()).toBe(true)
  })
})
```

```rust
// 后端单元测试示例 (Rust)
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    #[tokio::test]
    async fn test_chat_session_creation() {
        let session = ChatSession::new("test-session", "llama2-7b-chat").await;
        assert!(session.is_ok());

        let session = session.unwrap();
        assert_eq!(session.model_id, "llama2-7b-chat");
        assert!(session.messages.is_empty());
    }

    #[tokio::test]
    async fn test_message_processing() {
        let mut session = ChatSession::new("test", "llama2-7b-chat").await.unwrap();
        let result = session.add_message("user", "Hello").await;

        assert!(result.is_ok());
        assert_eq!(session.messages.len(), 1);
        assert_eq!(session.messages[0].content, "Hello");
    }

    #[test]
    fn test_document_chunking() {
        let content = "This is a test document. It has multiple sentences.";
        let chunks = chunk_document(content, 20, 5);

        assert!(!chunks.is_empty());
        assert!(chunks[0].len() <= 25); // chunk_size + overlap
    }
}
```

**集成测试**
```typescript
// API集成测试
describe('Chat API Integration', () => {
  it('should create session and send message', async () => {
    // 创建会话
    const sessionResponse = await fetch('/api/chat/sessions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: 'Test Session',
        model_id: 'test-model'
      })
    })

    const session = await sessionResponse.json()
    expect(session.data.id).toBeDefined()

    // 发送消息
    const messageResponse = await fetch('/api/chat/send', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        session_id: session.data.id,
        message: 'Hello, AI!'
      })
    })

    const result = await messageResponse.json()
    expect(result.status).toBe('success')
  })
})
```

**端到端测试**
```typescript
// E2E测试示例 (Playwright)
import { test, expect } from '@playwright/test'

test('complete chat workflow', async ({ page }) => {
  // 启动应用
  await page.goto('/')

  // 创建新会话
  await page.click('[data-testid="new-chat-button"]')
  await page.fill('[data-testid="session-title"]', 'E2E Test Session')
  await page.click('[data-testid="create-session"]')

  // 发送消息
  await page.fill('[data-testid="message-input"]', 'Hello, AI!')
  await page.click('[data-testid="send-button"]')

  // 验证响应
  await expect(page.locator('[data-testid="message-list"]')).toContainText('Hello, AI!')
  await expect(page.locator('[data-testid="ai-response"]')).toBeVisible()
})

test('knowledge base upload', async ({ page }) => {
  await page.goto('/knowledge')

  // 创建知识库
  await page.click('[data-testid="create-kb-button"]')
  await page.fill('[data-testid="kb-name"]', 'Test KB')
  await page.click('[data-testid="create-kb"]')

  // 上传文档
  await page.setInputFiles('[data-testid="file-upload"]', 'test-document.pdf')
  await expect(page.locator('[data-testid="upload-progress"]')).toBeVisible()
  await expect(page.locator('[data-testid="document-list"]')).toContainText('test-document.pdf')
})
```

**性能测试**
```typescript
// 负载测试示例
import { check } from 'k6'
import http from 'k6/http'

export let options = {
  stages: [
    { duration: '2m', target: 10 }, // 逐步增加到10个用户
    { duration: '5m', target: 10 }, // 保持10个用户5分钟
    { duration: '2m', target: 20 }, // 增加到20个用户
    { duration: '5m', target: 20 }, // 保持20个用户5分钟
    { duration: '2m', target: 0 },  // 逐步减少到0
  ],
}

export default function() {
  // 测试聊天API性能
  let response = http.post('http://localhost:3000/api/chat/send',
    JSON.stringify({
      session_id: 'test-session',
      message: 'Performance test message'
    }),
    { headers: { 'Content-Type': 'application/json' } }
  )

  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 2000ms': (r) => r.timings.duration < 2000,
  })
}
```

### 9.6 性能优化

**前端性能优化**
- **代码分割**：按路由和功能模块进行代码分割
- **懒加载**：组件和资源的按需加载
- **缓存策略**：合理使用浏览器缓存和应用缓存
- **虚拟滚动**：大列表使用虚拟滚动技术
- **防抖节流**：用户输入和API调用的防抖处理

```typescript
// 代码分割示例
const ChatView = defineAsyncComponent(() => import('@/views/Chat/index.vue'))
const KnowledgeView = defineAsyncComponent(() => import('@/views/Knowledge/index.vue'))

// 虚拟滚动示例
import { VirtualList } from '@tanstack/vue-virtual'

// 防抖处理
import { debounce } from 'lodash-es'

const debouncedSearch = debounce(async (query: string) => {
  const results = await searchAPI(query)
  searchResults.value = results
}, 300)
```

**后端性能优化**
- **连接池**：数据库和HTTP连接池优化
- **缓存层**：Redis缓存热点数据
- **异步处理**：耗时操作异步化
- **批量操作**：数据库批量读写
- **索引优化**：数据库索引优化

```rust
// 连接池配置
use sqlx::sqlite::SqlitePoolOptions;

let pool = SqlitePoolOptions::new()
    .max_connections(20)
    .min_connections(5)
    .acquire_timeout(Duration::from_secs(30))
    .connect(&database_url)
    .await?;

// 异步任务处理
use tokio::task;

let handle = task::spawn(async move {
    process_document(document_id).await
});

// 批量插入优化
let mut tx = pool.begin().await?;
for chunk in document_chunks.chunks(100) {
    sqlx::query("INSERT INTO chunks (content, embedding) VALUES (?, ?)")
        .bind_all(chunk)
        .execute(&mut tx)
        .await?;
}
tx.commit().await?;
```

**AI推理优化**
- **模型量化**：使用4位或8位量化减少内存占用
- **批处理**：批量处理推理请求
- **缓存机制**：缓存常见查询结果
- **GPU优化**：合理分配GPU资源
- **模型预热**：预加载常用模型

```rust
// 模型量化配置
let model_config = ModelConfig {
    quantization: QuantizationType::Q4_0,
    gpu_layers: 32,
    context_size: 4096,
    batch_size: 8,
};

// 推理缓存
use moka::future::Cache;

let inference_cache = Cache::builder()
    .max_capacity(1000)
    .time_to_live(Duration::from_secs(3600))
    .build();

// 批处理推理
async fn batch_inference(requests: Vec<InferenceRequest>) -> Vec<InferenceResult> {
    let batch_size = 8;
    let mut results = Vec::new();

    for batch in requests.chunks(batch_size) {
        let batch_results = model.infer_batch(batch).await?;
        results.extend(batch_results);
    }

    results
}
```

## 10. 详细开发功能点代码实现

### 10.1 前端核心组件代码

#### 10.1.1 主题切换组件 (ThemeToggle.vue)
```vue
<template>
  <div class="theme-toggle">
    <n-button
      :type="isDark ? 'primary' : 'default'"
      circle
      @click="toggleTheme"
      :title="$t('common.toggleTheme')"
    >
      <template #icon>
        <n-icon>
          <SunIcon v-if="isDark" />
          <MoonIcon v-else />
        </n-icon>
      </template>
    </n-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import { SunIcon, MoonIcon } from '@/components/icons'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

const isDark = computed(() => themeStore.isDark)

const toggleTheme = () => {
  themeStore.toggleTheme()
}
</script>

<style scoped>
.theme-toggle {
  display: inline-flex;
  align-items: center;
}
</style>
```

#### 10.1.2 语言切换组件 (LanguageSwitch.vue)
```vue
<template>
  <n-dropdown
    :options="languageOptions"
    @select="handleLanguageChange"
    trigger="click"
  >
    <n-button circle>
      <template #icon>
        <n-icon>
          <LanguageIcon />
        </n-icon>
      </template>
    </n-button>
  </n-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NDropdown, NButton, NIcon } from 'naive-ui'
import { LanguageIcon } from '@/components/icons'
import { useI18nStore } from '@/stores/i18n'

const i18nStore = useI18nStore()

const languageOptions = computed(() => [
  {
    label: '中文',
    key: 'zh-CN',
    icon: () => '🇨🇳'
  },
  {
    label: 'English',
    key: 'en-US',
    icon: () => '🇺🇸'
  }
])

const handleLanguageChange = (key: string) => {
  i18nStore.setLocale(key)
}
</script>
```

#### 10.1.3 聊天消息组件 (ChatMessage.vue)
```vue
<template>
  <div
    class="chat-message"
    :class="{
      'user-message': message.role === 'user',
      'assistant-message': message.role === 'assistant',
      'system-message': message.role === 'system'
    }"
  >
    <div class="message-avatar">
      <n-avatar
        :src="avatarSrc"
        :fallback-src="defaultAvatar"
        size="small"
      />
    </div>

    <div class="message-content">
      <div class="message-header">
        <span class="message-role">{{ roleText }}</span>
        <span class="message-time">{{ formatTime(message.created_at) }}</span>
      </div>

      <div class="message-body">
        <MarkdownRenderer
          v-if="message.role === 'assistant'"
          :content="message.content"
          :enable-copy="true"
        />
        <div v-else class="plain-text">
          {{ message.content }}
        </div>

        <!-- 附件显示 -->
        <div v-if="message.attachments?.length" class="message-attachments">
          <AttachmentPreview
            v-for="attachment in message.attachments"
            :key="attachment.id"
            :attachment="attachment"
          />
        </div>
      </div>

      <div class="message-actions">
        <n-button-group size="small">
          <n-button @click="copyMessage" ghost>
            <template #icon>
              <n-icon><CopyIcon /></n-icon>
            </template>
            {{ $t('chat.copy') }}
          </n-button>

          <n-button
            v-if="message.role === 'assistant'"
            @click="regenerateMessage"
            ghost
          >
            <template #icon>
              <n-icon><RefreshIcon /></n-icon>
            </template>
            {{ $t('chat.regenerate') }}
          </n-button>

          <n-button @click="deleteMessage" ghost type="error">
            <template #icon>
              <n-icon><DeleteIcon /></n-icon>
            </template>
            {{ $t('chat.delete') }}
          </n-button>
        </n-button-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NAvatar, NButton, NButtonGroup, NIcon } from 'naive-ui'
import { CopyIcon, RefreshIcon, DeleteIcon } from '@/components/icons'
import MarkdownRenderer from '@/components/common/MarkdownRenderer.vue'
import AttachmentPreview from '@/components/common/AttachmentPreview.vue'
import { useChatStore } from '@/stores/chat'
import { useI18n } from 'vue-i18n'
import { formatDistanceToNow } from 'date-fns'
import { zhCN, enUS } from 'date-fns/locale'

interface Props {
  message: ChatMessage
}

const props = defineProps<Props>()
const emit = defineEmits<{
  regenerate: [messageId: string]
  delete: [messageId: string]
}>()

const { t, locale } = useI18n()
const chatStore = useChatStore()

const avatarSrc = computed(() => {
  if (props.message.role === 'user') {
    return chatStore.userAvatar
  } else if (props.message.role === 'assistant') {
    return '/icons/ai-avatar.png'
  }
  return '/icons/system-avatar.png'
})

const defaultAvatar = computed(() => {
  return props.message.role === 'user' ? '👤' : '🤖'
})

const roleText = computed(() => {
  return t(`chat.roles.${props.message.role}`)
})

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const localeMap = {
    'zh-CN': zhCN,
    'en-US': enUS
  }

  return formatDistanceToNow(date, {
    addSuffix: true,
    locale: localeMap[locale.value as keyof typeof localeMap]
  })
}

const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(props.message.content)
    window.$message.success(t('chat.copySuccess'))
  } catch (error) {
    window.$message.error(t('chat.copyFailed'))
  }
}

const regenerateMessage = () => {
  emit('regenerate', props.message.id)
}

const deleteMessage = () => {
  emit('delete', props.message.id)
}
</script>

<style scoped>
.chat-message {
  display: flex;
  gap: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.chat-message:hover {
  background-color: var(--n-color-hover);
}

.user-message {
  flex-direction: row-reverse;
}

.user-message .message-content {
  text-align: right;
}

.assistant-message .message-content {
  text-align: left;
}

.system-message {
  justify-content: center;
  opacity: 0.7;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  opacity: 0.7;
}

.message-body {
  margin-bottom: 8px;
}

.plain-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.message-attachments {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.message-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.chat-message:hover .message-actions {
  opacity: 1;
}
</style>
```

### 10.2 状态管理 (Pinia Store)

#### 10.2.1 主题状态管理 (stores/theme.ts)
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { darkTheme, lightTheme } from 'naive-ui'

export const useThemeStore = defineStore('theme', () => {
  const isDark = ref(false)

  // 从本地存储加载主题设置
  const loadTheme = () => {
    const saved = localStorage.getItem('ai-studio-theme')
    if (saved) {
      isDark.value = saved === 'dark'
    } else {
      // 检测系统主题偏好
      isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    applyTheme()
  }

  // 应用主题
  const applyTheme = () => {
    document.documentElement.setAttribute('data-theme', isDark.value ? 'dark' : 'light')
    localStorage.setItem('ai-studio-theme', isDark.value ? 'dark' : 'light')
  }

  // 切换主题
  const toggleTheme = () => {
    isDark.value = !isDark.value
    applyTheme()
  }

  // 设置主题
  const setTheme = (theme: 'light' | 'dark') => {
    isDark.value = theme === 'dark'
    applyTheme()
  }

  // Naive UI 主题配置
  const naiveTheme = computed(() => {
    return isDark.value ? darkTheme : lightTheme
  })

  // 主题变量
  const themeVars = computed(() => {
    if (isDark.value) {
      return {
        primaryColor: '#18a058',
        primaryColorHover: '#36ad6a',
        primaryColorPressed: '#0c7a43',
        primaryColorSuppl: '#36ad6a',
        infoColor: '#2080f0',
        successColor: '#18a058',
        warningColor: '#f0a020',
        errorColor: '#d03050',
        textColorBase: '#ffffff',
        textColor1: 'rgba(255, 255, 255, 0.9)',
        textColor2: 'rgba(255, 255, 255, 0.82)',
        textColor3: 'rgba(255, 255, 255, 0.52)',
        bodyColor: '#101014',
        cardColor: '#18181c',
        modalColor: '#1e1e20',
        popoverColor: '#1e1e20',
        tableHeaderColor: '#1e1e20',
        inputColor: 'rgba(255, 255, 255, 0.1)',
        codeColor: 'rgba(255, 255, 255, 0.12)',
        tabColor: 'rgba(255, 255, 255, 0.04)',
        actionColor: 'rgba(255, 255, 255, 0.06)',
        tableColorHover: 'rgba(255, 255, 255, 0.06)',
        tableColorStriped: 'rgba(255, 255, 255, 0.02)',
        hoverColor: 'rgba(255, 255, 255, 0.09)',
        dividerColor: 'rgba(255, 255, 255, 0.09)',
        borderColor: 'rgba(255, 255, 255, 0.24)',
        closeIconColor: 'rgba(255, 255, 255, 0.52)',
        closeIconColorHover: 'rgba(255, 255, 255, 0.82)',
        closeIconColorPressed: 'rgba(255, 255, 255, 0.9)',
        clearColor: 'rgba(255, 255, 255, 0.52)',
        clearColorHover: 'rgba(255, 255, 255, 0.82)',
        clearColorPressed: 'rgba(255, 255, 255, 0.9)',
        scrollbarColor: 'rgba(255, 255, 255, 0.2)',
        scrollbarColorHover: 'rgba(255, 255, 255, 0.3)',
        boxShadow1: '0 1px 2px -2px rgba(0, 0, 0, .24), 0 3px 6px 0 rgba(0, 0, 0, .18), 0 5px 12px 4px rgba(0, 0, 0, .12)',
        boxShadow2: '0 3px 6px -4px rgba(0, 0, 0, .24), 0 6px 16px 0 rgba(0, 0, 0, .18), 0 9px 28px 8px rgba(0, 0, 0, .12)',
        boxShadow3: '0 6px 16px -9px rgba(0, 0, 0, .24), 0 9px 28px 0 rgba(0, 0, 0, .18), 0 12px 48px 16px rgba(0, 0, 0, .12)'
      }
    } else {
      return {
        primaryColor: '#18a058',
        primaryColorHover: '#36ad6a',
        primaryColorPressed: '#0c7a43',
        primaryColorSuppl: '#36ad6a',
        infoColor: '#2080f0',
        successColor: '#18a058',
        warningColor: '#f0a020',
        errorColor: '#d03050',
        textColorBase: '#000000',
        textColor1: 'rgba(0, 0, 0, 0.9)',
        textColor2: 'rgba(0, 0, 0, 0.82)',
        textColor3: 'rgba(0, 0, 0, 0.52)',
        bodyColor: '#ffffff',
        cardColor: '#ffffff',
        modalColor: '#ffffff',
        popoverColor: '#ffffff',
        tableHeaderColor: '#fafafa',
        inputColor: '#ffffff',
        codeColor: 'rgba(0, 0, 0, 0.06)',
        tabColor: 'rgba(0, 0, 0, 0.04)',
        actionColor: 'rgba(0, 0, 0, 0.02)',
        tableColorHover: 'rgba(0, 0, 0, 0.02)',
        tableColorStriped: 'rgba(0, 0, 0, 0.01)',
        hoverColor: 'rgba(0, 0, 0, 0.03)',
        dividerColor: 'rgba(0, 0, 0, 0.06)',
        borderColor: 'rgba(0, 0, 0, 0.15)',
        closeIconColor: 'rgba(0, 0, 0, 0.45)',
        closeIconColorHover: 'rgba(0, 0, 0, 0.65)',
        closeIconColorPressed: 'rgba(0, 0, 0, 0.85)',
        clearColor: 'rgba(0, 0, 0, 0.45)',
        clearColorHover: 'rgba(0, 0, 0, 0.65)',
        clearColorPressed: 'rgba(0, 0, 0, 0.85)',
        scrollbarColor: 'rgba(0, 0, 0, 0.25)',
        scrollbarColorHover: 'rgba(0, 0, 0, 0.4)',
        boxShadow1: '0 1px 2px -2px rgba(0, 0, 0, .08), 0 3px 6px 0 rgba(0, 0, 0, .06), 0 5px 12px 4px rgba(0, 0, 0, .04)',
        boxShadow2: '0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05)',
        boxShadow3: '0 6px 16px -9px rgba(0, 0, 0, .08), 0 9px 28px 0 rgba(0, 0, 0, .06), 0 12px 48px 16px rgba(0, 0, 0, .03)'
      }
    }
  })

  return {
    isDark,
    naiveTheme,
    themeVars,
    loadTheme,
    toggleTheme,
    setTheme
  }
})
```

#### 10.2.2 聊天状态管理 (stores/chat.ts)
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { chatAPI } from '@/api/chat'
import type { ChatSession, ChatMessage, ModelConfig } from '@/types/chat'

export const useChatStore = defineStore('chat', () => {
  const sessions = ref<ChatSession[]>([])
  const currentSessionId = ref<string | null>(null)
  const messages = ref<ChatMessage[]>([])
  const isLoading = ref(false)
  const isStreaming = ref(false)
  const userAvatar = ref('/avatars/default-user.png')

  // 计算属性
  const currentSession = computed(() => {
    return sessions.value.find(s => s.id === currentSessionId.value)
  })

  const sortedSessions = computed(() => {
    return [...sessions.value].sort((a, b) =>
      new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    )
  })

  // 加载会话列表
  const loadSessions = async () => {
    try {
      const response = await chatAPI.getSessions()
      sessions.value = response.data.items
    } catch (error) {
      console.error('Failed to load sessions:', error)
      throw error
    }
  }

  // 创建新会话
  const createSession = async (title?: string, modelId?: string) => {
    try {
      const response = await chatAPI.createSession({
        title: title || `新对话 ${new Date().toLocaleString()}`,
        model_id: modelId,
        system_prompt: '',
        config: {
          temperature: 0.7,
          max_tokens: 2048,
          top_p: 0.9
        }
      })

      const newSession = response.data
      sessions.value.unshift(newSession)
      currentSessionId.value = newSession.id
      messages.value = []

      return newSession
    } catch (error) {
      console.error('Failed to create session:', error)
      throw error
    }
  }

  // 切换会话
  const switchSession = async (sessionId: string) => {
    if (currentSessionId.value === sessionId) return

    currentSessionId.value = sessionId
    await loadMessages(sessionId)
  }

  // 加载消息
  const loadMessages = async (sessionId: string) => {
    try {
      isLoading.value = true
      const response = await chatAPI.getMessages(sessionId)
      messages.value = response.data.items
    } catch (error) {
      console.error('Failed to load messages:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 发送消息
  const sendMessage = async (
    content: string,
    attachments?: any[],
    modelConfig?: ModelConfig
  ) => {
    if (!currentSessionId.value) {
      throw new Error('No active session')
    }

    try {
      isLoading.value = true

      // 添加用户消息到本地状态
      const userMessage: ChatMessage = {
        id: `temp-${Date.now()}`,
        session_id: currentSessionId.value,
        role: 'user',
        content,
        attachments,
        created_at: new Date().toISOString()
      }
      messages.value.push(userMessage)

      // 发送到后端
      const response = await chatAPI.sendMessage({
        session_id: currentSessionId.value,
        message: content,
        attachments,
        model_config: modelConfig
      })

      // 更新用户消息ID
      const messageIndex = messages.value.findIndex(m => m.id === userMessage.id)
      if (messageIndex !== -1) {
        messages.value[messageIndex].id = response.data.message_id
      }

      // 开始接收流式响应
      await receiveStreamResponse(response.data.message_id)

    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 接收流式响应
  const receiveStreamResponse = async (messageId: string) => {
    return new Promise<void>((resolve, reject) => {
      isStreaming.value = true

      // 创建AI消息占位符
      const aiMessage: ChatMessage = {
        id: `ai-${messageId}`,
        session_id: currentSessionId.value!,
        role: 'assistant',
        content: '',
        created_at: new Date().toISOString()
      }
      messages.value.push(aiMessage)

      const eventSource = new EventSource(`/api/chat/stream/${messageId}`)

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)

          if (data.type === 'token') {
            // 更新消息内容
            const messageIndex = messages.value.findIndex(m => m.id === aiMessage.id)
            if (messageIndex !== -1) {
              messages.value[messageIndex].content += data.content
            }
          } else if (data.type === 'done') {
            // 响应完成
            const messageIndex = messages.value.findIndex(m => m.id === aiMessage.id)
            if (messageIndex !== -1) {
              messages.value[messageIndex].id = data.message_id || aiMessage.id
              messages.value[messageIndex].tokens_used = data.metadata?.tokens_used
              messages.value[messageIndex].response_time = data.metadata?.response_time
            }

            eventSource.close()
            isStreaming.value = false
            resolve()
          } else if (data.type === 'error') {
            // 处理错误
            console.error('Stream error:', data.content)
            eventSource.close()
            isStreaming.value = false
            reject(new Error(data.content))
          }
        } catch (error) {
          console.error('Failed to parse stream data:', error)
        }
      }

      eventSource.onerror = (error) => {
        console.error('EventSource error:', error)
        eventSource.close()
        isStreaming.value = false
        reject(error)
      }
    })
  }

  // 重新生成消息
  const regenerateMessage = async (messageId: string) => {
    const messageIndex = messages.value.findIndex(m => m.id === messageId)
    if (messageIndex === -1) return

    const message = messages.value[messageIndex]
    if (message.role !== 'assistant') return

    // 找到对应的用户消息
    const userMessageIndex = messageIndex - 1
    if (userMessageIndex < 0) return

    const userMessage = messages.value[userMessageIndex]

    // 删除AI消息
    messages.value.splice(messageIndex, 1)

    // 重新发送用户消息
    await sendMessage(userMessage.content, userMessage.attachments)
  }

  // 删除消息
  const deleteMessage = async (messageId: string) => {
    const messageIndex = messages.value.findIndex(m => m.id === messageId)
    if (messageIndex !== -1) {
      messages.value.splice(messageIndex, 1)
    }
  }

  // 更新会话
  const updateSession = async (sessionId: string, updates: Partial<ChatSession>) => {
    try {
      const response = await chatAPI.updateSession(sessionId, updates)
      const sessionIndex = sessions.value.findIndex(s => s.id === sessionId)
      if (sessionIndex !== -1) {
        sessions.value[sessionIndex] = { ...sessions.value[sessionIndex], ...response.data }
      }
    } catch (error) {
      console.error('Failed to update session:', error)
      throw error
    }
  }

  // 删除会话
  const deleteSession = async (sessionId: string) => {
    try {
      await chatAPI.deleteSession(sessionId)
      const sessionIndex = sessions.value.findIndex(s => s.id === sessionId)
      if (sessionIndex !== -1) {
        sessions.value.splice(sessionIndex, 1)
      }

      if (currentSessionId.value === sessionId) {
        currentSessionId.value = sessions.value[0]?.id || null
        if (currentSessionId.value) {
          await loadMessages(currentSessionId.value)
        } else {
          messages.value = []
        }
      }
    } catch (error) {
      console.error('Failed to delete session:', error)
      throw error
    }
  }

  // 清空当前会话消息
  const clearCurrentSession = async () => {
    if (!currentSessionId.value) return

    try {
      // 这里可以调用API清空会话消息
      messages.value = []
    } catch (error) {
      console.error('Failed to clear session:', error)
      throw error
    }
  }

  return {
    // 状态
    sessions,
    currentSessionId,
    messages,
    isLoading,
    isStreaming,
    userAvatar,

    // 计算属性
    currentSession,
    sortedSessions,

    // 方法
    loadSessions,
    createSession,
    switchSession,
    loadMessages,
    sendMessage,
    regenerateMessage,
    deleteMessage,
    updateSession,
    deleteSession,
    clearCurrentSession
  }
})
```

### 10.3 后端核心模块代码

#### 10.3.1 聊天服务模块 (src-tauri/src/chat/mod.rs)
```rust
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::{mpsc, RwLock};
use uuid::Uuid;
use anyhow::Result;

pub mod session;
pub mod message;
pub mod streaming;

use session::ChatSession;
use message::ChatMessage;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatConfig {
    pub model_id: String,
    pub temperature: f32,
    pub max_tokens: u32,
    pub top_p: f32,
    pub system_prompt: Option<String>,
}

impl Default for ChatConfig {
    fn default() -> Self {
        Self {
            model_id: "default".to_string(),
            temperature: 0.7,
            max_tokens: 2048,
            top_p: 0.9,
            system_prompt: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub session_id: String,
    pub message: String,
    pub attachments: Option<Vec<MessageAttachment>>,
    pub model_config: Option<ChatConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageAttachment {
    pub id: String,
    pub r#type: String, // image, document, audio
    pub url: String,
    pub name: String,
    pub size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamToken {
    pub r#type: String, // token, done, error
    pub content: String,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

pub struct ChatService {
    sessions: RwLock<HashMap<String, ChatSession>>,
    active_streams: RwLock<HashMap<String, mpsc::Sender<StreamToken>>>,
}

impl ChatService {
    pub fn new() -> Self {
        Self {
            sessions: RwLock::new(HashMap::new()),
            active_streams: RwLock::new(HashMap::new()),
        }
    }

    pub async fn create_session(
        &self,
        title: Option<String>,
        config: Option<ChatConfig>,
    ) -> Result<ChatSession> {
        let session_id = Uuid::new_v4().to_string();
        let session = ChatSession::new(
            session_id.clone(),
            title.unwrap_or_else(|| format!("Chat {}", chrono::Utc::now().format("%Y-%m-%d %H:%M"))),
            config.unwrap_or_default(),
        );

        // 保存到数据库
        session.save_to_db().await?;

        // 缓存到内存
        self.sessions.write().await.insert(session_id, session.clone());

        Ok(session)
    }

    pub async fn get_session(&self, session_id: &str) -> Result<Option<ChatSession>> {
        // 先从内存缓存查找
        if let Some(session) = self.sessions.read().await.get(session_id) {
            return Ok(Some(session.clone()));
        }

        // 从数据库加载
        if let Some(session) = ChatSession::load_from_db(session_id).await? {
            self.sessions.write().await.insert(session_id.to_string(), session.clone());
            Ok(Some(session))
        } else {
            Ok(None)
        }
    }

    pub async fn send_message(
        &self,
        request: SendMessageRequest,
    ) -> Result<String> {
        let session = self.get_session(&request.session_id).await?
            .ok_or_else(|| anyhow::anyhow!("Session not found"))?;

        // 创建用户消息
        let user_message = ChatMessage::new(
            request.session_id.clone(),
            "user".to_string(),
            request.message,
            request.attachments,
        );

        // 保存用户消息
        user_message.save_to_db().await?;

        // 创建AI响应消息
        let ai_message_id = Uuid::new_v4().to_string();
        let ai_message = ChatMessage::new(
            request.session_id.clone(),
            "assistant".to_string(),
            String::new(),
            None,
        );

        // 开始流式推理
        self.start_inference(
            ai_message_id.clone(),
            session,
            user_message,
            request.model_config,
        ).await?;

        Ok(ai_message_id)
    }

    async fn start_inference(
        &self,
        message_id: String,
        session: ChatSession,
        user_message: ChatMessage,
        config: Option<ChatConfig>,
    ) -> Result<()> {
        let (tx, mut rx) = mpsc::channel::<StreamToken>(100);

        // 注册流
        self.active_streams.write().await.insert(message_id.clone(), tx);

        // 启动推理任务
        let inference_config = config.unwrap_or(session.config);
        tokio::spawn(async move {
            // 这里调用AI推理引擎
            // 示例：调用本地模型或远程API
            match perform_inference(&session, &user_message, &inference_config).await {
                Ok(response_stream) => {
                    // 处理流式响应
                    let mut content = String::new();

                    while let Some(token) = response_stream.next().await {
                        content.push_str(&token);

                        let stream_token = StreamToken {
                            r#type: "token".to_string(),
                            content: token,
                            metadata: None,
                        };

                        if tx.send(stream_token).await.is_err() {
                            break;
                        }
                    }

                    // 发送完成信号
                    let done_token = StreamToken {
                        r#type: "done".to_string(),
                        content: String::new(),
                        metadata: Some({
                            let mut meta = HashMap::new();
                            meta.insert("tokens_used".to_string(), serde_json::Value::Number(content.len().into()));
                            meta.insert("response_time".to_string(), serde_json::Value::Number(1000.into()));
                            meta
                        }),
                    };

                    let _ = tx.send(done_token).await;

                    // 保存完整的AI消息到数据库
                    let ai_message = ChatMessage::new(
                        session.id,
                        "assistant".to_string(),
                        content,
                        None,
                    );
                    let _ = ai_message.save_to_db().await;
                }
                Err(error) => {
                    let error_token = StreamToken {
                        r#type: "error".to_string(),
                        content: error.to_string(),
                        metadata: None,
                    };
                    let _ = tx.send(error_token).await;
                }
            }
        });

        Ok(())
    }

    pub async fn get_stream_receiver(&self, message_id: &str) -> Option<mpsc::Receiver<StreamToken>> {
        // 这里需要实现流接收器的获取逻辑
        // 由于Rust的所有权限制，这里需要特殊处理
        None
    }
}

// AI推理函数（示例）
async fn perform_inference(
    session: &ChatSession,
    user_message: &ChatMessage,
    config: &ChatConfig,
) -> Result<impl futures::Stream<Item = String>> {
    // 这里是AI推理的具体实现
    // 可以调用本地模型（如llama.cpp）或远程API

    use futures::stream;

    // 示例：返回一个模拟的流
    let tokens = vec![
        "Hello".to_string(),
        " there".to_string(),
        "! How".to_string(),
        " can".to_string(),
        " I".to_string(),
        " help".to_string(),
        " you".to_string(),
        " today".to_string(),
        "?".to_string(),
    ];

    Ok(stream::iter(tokens))
}
```

## 11. 完整数据库设计方案

### 11.1 数据库架构设计

AI Studio 采用混合数据库架构，结合关系型数据库和向量数据库的优势：

- **SQLite 主数据库**：存储结构化数据，如用户信息、会话记录、系统配置等
- **ChromaDB 向量数据库**：存储文档向量和实现语义搜索
- **本地文件存储**：存储模型文件、文档文件、媒体文件等

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application)                     │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (DAL)                         │
│  Repository Pattern + ORM + Connection Pool                │
├─────────────────────────────────────────────────────────────┤
│  SQLite 主数据库    │  ChromaDB 向量库  │  本地文件存储     │
│  - 用户数据         │  - 文档向量       │  - 模型文件       │
│  - 会话记录         │  - 语义搜索       │  - 文档文件       │
│  - 系统配置         │  - 知识图谱       │  - 媒体文件       │
│  - 元数据管理       │  - 相似度计算     │  - 缓存文件       │
└─────────────────────────────────────────────────────────────┘
```

### 11.2 SQLite 主数据库设计

#### 11.2.1 核心表结构

**用户和认证表**
```sql
-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    username TEXT UNIQUE NOT NULL,
    display_name TEXT NOT NULL,
    email TEXT UNIQUE,
    avatar_url TEXT,
    preferences JSON DEFAULT '{}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME,
    is_active BOOLEAN DEFAULT 1
);

-- 用户会话表（登录会话）
CREATE TABLE user_sessions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,
    user_agent TEXT
);

-- 用户设置表
CREATE TABLE user_settings (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category TEXT NOT NULL, -- 'appearance', 'language', 'ai', 'privacy'
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, category, key)
);
```

**聊天相关表**
```sql
-- 聊天会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    model_id TEXT,
    system_prompt TEXT,
    config JSON DEFAULT '{}', -- 温度、最大token等配置
    message_count INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_archived BOOLEAN DEFAULT 0,
    is_pinned BOOLEAN DEFAULT 0
);

-- 聊天消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    session_id TEXT NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    parent_id TEXT REFERENCES chat_messages(id), -- 支持消息树结构
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    content_type TEXT DEFAULT 'text', -- 'text', 'markdown', 'code'
    attachments JSON, -- 附件信息
    metadata JSON, -- 额外元数据
    tokens_used INTEGER,
    response_time REAL, -- 响应时间（秒）
    model_used TEXT, -- 使用的模型
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT 0
);

-- 消息反馈表
CREATE TABLE message_feedback (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    message_id TEXT NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    feedback_type TEXT NOT NULL CHECK (feedback_type IN ('like', 'dislike', 'report')),
    comment TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, user_id, feedback_type)
);
```

**知识库相关表**
```sql
-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    embedding_model TEXT DEFAULT 'sentence-transformers/all-MiniLM-L6-v2',
    chunk_size INTEGER DEFAULT 512,
    chunk_overlap INTEGER DEFAULT 50,
    document_count INTEGER DEFAULT 0,
    total_chunks INTEGER DEFAULT 0,
    total_size INTEGER DEFAULT 0, -- 总大小（字节）
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'processing', 'error', 'archived')),
    config JSON DEFAULT '{}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_indexed_at DATETIME
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    kb_id TEXT NOT NULL REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_type TEXT NOT NULL, -- 'pdf', 'docx', 'txt', 'md', 'html'
    mime_type TEXT,
    file_size INTEGER NOT NULL,
    file_path TEXT NOT NULL, -- 本地文件路径
    file_hash TEXT NOT NULL, -- 文件哈希值，用于去重
    content_preview TEXT, -- 内容预览（前500字符）
    page_count INTEGER, -- 页数（适用于PDF等）
    word_count INTEGER, -- 字数
    language TEXT DEFAULT 'auto', -- 文档语言
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'archived')),
    processing_progress REAL DEFAULT 0.0, -- 处理进度 0.0-1.0
    error_message TEXT, -- 错误信息
    chunks_count INTEGER DEFAULT 0,
    metadata JSON, -- 文档元数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,
    UNIQUE(kb_id, file_hash) -- 同一知识库内文件去重
);

-- 文档块表（用于存储分块信息）
CREATE TABLE document_chunks (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    document_id TEXT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    kb_id TEXT NOT NULL REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL, -- 块索引
    content TEXT NOT NULL, -- 块内容
    content_hash TEXT NOT NULL, -- 内容哈希
    token_count INTEGER, -- token数量
    page_number INTEGER, -- 页码（如果适用）
    section_title TEXT, -- 章节标题
    metadata JSON, -- 块元数据
    vector_id TEXT, -- 对应的向量ID（ChromaDB中的ID）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(document_id, chunk_index)
);

-- 知识库搜索历史
CREATE TABLE kb_search_history (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    kb_id TEXT NOT NULL REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    results_count INTEGER DEFAULT 0,
    search_time REAL, -- 搜索耗时
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**模型管理相关表**
```sql
-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    name TEXT NOT NULL, -- 模型标识符
    display_name TEXT NOT NULL, -- 显示名称
    description TEXT,
    author TEXT,
    version TEXT,
    model_type TEXT NOT NULL CHECK (model_type IN ('local', 'remote', 'api')),
    architecture TEXT, -- 'llama', 'mistral', 'qwen', 'gpt', 'claude'
    parameter_count TEXT, -- 参数量 '7B', '13B', '70B'
    quantization TEXT, -- 量化类型 'none', 'q4_0', 'q4_1', 'q8_0'
    context_length INTEGER DEFAULT 2048,
    file_size INTEGER, -- 文件大小（字节）
    local_path TEXT, -- 本地文件路径
    download_url TEXT, -- 下载URL
    huggingface_id TEXT, -- HuggingFace模型ID
    config JSON DEFAULT '{}', -- 模型配置
    capabilities JSON DEFAULT '[]', -- 模型能力 ['chat', 'completion', 'embedding']
    status TEXT DEFAULT 'available' CHECK (status IN ('available', 'downloading', 'loaded', 'error', 'archived')),
    load_time REAL, -- 加载时间
    memory_usage INTEGER, -- 内存使用量（MB）
    gpu_memory_usage INTEGER, -- GPU内存使用量（MB）
    performance_score REAL, -- 性能评分
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used_at DATETIME,
    UNIQUE(name, version)
);

-- 模型下载任务表
CREATE TABLE model_downloads (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    model_id TEXT REFERENCES models(id) ON DELETE CASCADE,
    download_url TEXT NOT NULL,
    local_path TEXT NOT NULL,
    total_size INTEGER, -- 总大小
    downloaded_size INTEGER DEFAULT 0, -- 已下载大小
    progress REAL DEFAULT 0.0, -- 下载进度 0.0-1.0
    speed INTEGER DEFAULT 0, -- 下载速度（字节/秒）
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'paused', 'completed', 'failed', 'cancelled')),
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- 模型使用统计表
CREATE TABLE model_usage_stats (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    model_id TEXT NOT NULL REFERENCES models(id) ON DELETE CASCADE,
    user_id TEXT REFERENCES users(id) ON DELETE SET NULL,
    session_id TEXT REFERENCES chat_sessions(id) ON DELETE SET NULL,
    usage_type TEXT NOT NULL CHECK (usage_type IN ('chat', 'completion', 'embedding')),
    tokens_input INTEGER DEFAULT 0,
    tokens_output INTEGER DEFAULT 0,
    response_time REAL, -- 响应时间（秒）
    memory_peak INTEGER, -- 峰值内存使用（MB）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**多模态处理相关表**
```sql
-- 多模态任务表
CREATE TABLE multimodal_tasks (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    task_type TEXT NOT NULL CHECK (task_type IN ('ocr', 'tts', 'asr', 'image_analysis', 'video_analysis', 'format_conversion')),
    input_file_path TEXT NOT NULL,
    input_file_type TEXT NOT NULL,
    input_file_size INTEGER NOT NULL,
    output_file_path TEXT,
    output_file_type TEXT,
    output_file_size INTEGER,
    parameters JSON DEFAULT '{}', -- 任务参数
    progress REAL DEFAULT 0.0,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    error_message TEXT,
    processing_time REAL, -- 处理时间（秒）
    result_data JSON, -- 处理结果数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- OCR结果表
CREATE TABLE ocr_results (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    task_id TEXT NOT NULL REFERENCES multimodal_tasks(id) ON DELETE CASCADE,
    page_number INTEGER DEFAULT 1,
    text_content TEXT NOT NULL,
    confidence REAL, -- 识别置信度
    bounding_boxes JSON, -- 边界框信息
    language TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 11.3 数据库索引设计

```sql
-- 性能优化索引
CREATE INDEX idx_chat_sessions_user_updated ON chat_sessions(user_id, updated_at DESC);
CREATE INDEX idx_chat_messages_session_created ON chat_messages(session_id, created_at);
CREATE INDEX idx_chat_messages_role ON chat_messages(role);
CREATE INDEX idx_documents_kb_status ON documents(kb_id, status);
CREATE INDEX idx_document_chunks_document ON document_chunks(document_id, chunk_index);
CREATE INDEX idx_models_type_status ON models(model_type, status);
CREATE INDEX idx_model_downloads_status ON model_downloads(status, created_at);
CREATE INDEX idx_multimodal_tasks_user_type ON multimodal_tasks(user_id, task_type, created_at DESC);

-- 全文搜索索引
CREATE VIRTUAL TABLE chat_messages_fts USING fts5(
    content,
    content=chat_messages,
    content_rowid=rowid
);

CREATE VIRTUAL TABLE documents_fts USING fts5(
    name,
    content_preview,
    content=documents,
    content_rowid=rowid
);

-- 触发器维护FTS索引
CREATE TRIGGER chat_messages_fts_insert AFTER INSERT ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;

CREATE TRIGGER chat_messages_fts_delete AFTER DELETE ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(chat_messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
END;

CREATE TRIGGER chat_messages_fts_update AFTER UPDATE ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(chat_messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
    INSERT INTO chat_messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;
```

### 11.4 ChromaDB 向量数据库设计

#### 11.4.1 向量数据库架构

ChromaDB 用于存储文档向量和实现高效的语义搜索：

```python
# ChromaDB 配置和初始化
import chromadb
from chromadb.config import Settings

# 数据库配置
CHROMA_CONFIG = {
    "chroma_db_impl": "duckdb+parquet",
    "persist_directory": "./data/chroma",
    "anonymized_telemetry": False,
    "allow_reset": True
}

# 初始化客户端
client = chromadb.Client(Settings(**CHROMA_CONFIG))

# Collection 命名规范
# ai_studio_kb_{knowledge_base_id}
# 例如: ai_studio_kb_abc123def456
```

#### 11.4.2 Collection 结构设计

```python
# 知识库 Collection 结构
class KnowledgeBaseCollection:
    def __init__(self, kb_id: str, embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"):
        self.collection_name = f"ai_studio_kb_{kb_id}"
        self.embedding_model = embedding_model
        self.collection = client.get_or_create_collection(
            name=self.collection_name,
            metadata={
                "kb_id": kb_id,
                "embedding_model": embedding_model,
                "created_at": datetime.utcnow().isoformat(),
                "version": "1.0"
            }
        )

    def add_document_chunks(self, chunks: List[DocumentChunk]):
        """添加文档块到向量数据库"""
        documents = []
        metadatas = []
        ids = []

        for chunk in chunks:
            documents.append(chunk.content)
            metadatas.append({
                "document_id": chunk.document_id,
                "chunk_index": chunk.chunk_index,
                "page_number": chunk.page_number,
                "section_title": chunk.section_title,
                "token_count": chunk.token_count,
                "created_at": chunk.created_at.isoformat(),
                "document_name": chunk.document_name,
                "document_type": chunk.document_type
            })
            ids.append(chunk.vector_id)

        self.collection.add(
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )

    def search_similar(self, query: str, n_results: int = 10, filters: dict = None):
        """语义搜索"""
        where_clause = {}
        if filters:
            if "document_types" in filters:
                where_clause["document_type"] = {"$in": filters["document_types"]}
            if "date_range" in filters:
                where_clause["created_at"] = {
                    "$gte": filters["date_range"]["start"],
                    "$lte": filters["date_range"]["end"]
                }

        results = self.collection.query(
            query_texts=[query],
            n_results=n_results,
            where=where_clause if where_clause else None,
            include=["documents", "metadatas", "distances"]
        )

        return results

    def delete_document(self, document_id: str):
        """删除文档的所有向量"""
        self.collection.delete(
            where={"document_id": document_id}
        )

    def update_chunk(self, chunk_id: str, content: str, metadata: dict):
        """更新文档块"""
        self.collection.update(
            ids=[chunk_id],
            documents=[content],
            metadatas=[metadata]
        )

    def get_collection_stats(self):
        """获取Collection统计信息"""
        return {
            "total_chunks": self.collection.count(),
            "metadata": self.collection.metadata
        }
```

#### 11.4.3 向量搜索优化

```python
# 高级搜索功能
class AdvancedVectorSearch:
    def __init__(self, collection: chromadb.Collection):
        self.collection = collection

    def hybrid_search(self, query: str, keywords: List[str] = None,
                     semantic_weight: float = 0.7, keyword_weight: float = 0.3):
        """混合搜索：语义搜索 + 关键词搜索"""
        # 语义搜索
        semantic_results = self.collection.query(
            query_texts=[query],
            n_results=20,
            include=["documents", "metadatas", "distances"]
        )

        # 关键词过滤
        if keywords:
            filtered_results = []
            for i, doc in enumerate(semantic_results["documents"][0]):
                keyword_score = sum(1 for kw in keywords if kw.lower() in doc.lower()) / len(keywords)
                semantic_score = 1 - semantic_results["distances"][0][i]  # 转换为相似度

                combined_score = semantic_weight * semantic_score + keyword_weight * keyword_score

                filtered_results.append({
                    "document": doc,
                    "metadata": semantic_results["metadatas"][0][i],
                    "score": combined_score,
                    "semantic_score": semantic_score,
                    "keyword_score": keyword_score
                })

            # 按综合得分排序
            filtered_results.sort(key=lambda x: x["score"], reverse=True)
            return filtered_results[:10]

        return semantic_results

    def multi_query_search(self, queries: List[str], aggregation: str = "max"):
        """多查询搜索"""
        all_results = []

        for query in queries:
            results = self.collection.query(
                query_texts=[query],
                n_results=10,
                include=["documents", "metadatas", "distances"]
            )
            all_results.append(results)

        # 聚合结果
        if aggregation == "max":
            # 取最高相似度
            return self._aggregate_max_similarity(all_results)
        elif aggregation == "avg":
            # 取平均相似度
            return self._aggregate_avg_similarity(all_results)

        return all_results[0]  # 默认返回第一个查询的结果

    def contextual_search(self, query: str, context_window: int = 2):
        """上下文搜索：返回匹配块及其前后文"""
        results = self.collection.query(
            query_texts=[query],
            n_results=10,
            include=["documents", "metadatas", "distances"]
        )

        contextual_results = []
        for i, metadata in enumerate(results["metadatas"][0]):
            document_id = metadata["document_id"]
            chunk_index = metadata["chunk_index"]

            # 获取上下文块
            context_chunks = self._get_context_chunks(
                document_id, chunk_index, context_window
            )

            contextual_results.append({
                "main_chunk": results["documents"][0][i],
                "context_chunks": context_chunks,
                "metadata": metadata,
                "score": 1 - results["distances"][0][i]
            })

        return contextual_results

    def _get_context_chunks(self, document_id: str, chunk_index: int, window: int):
        """获取上下文块"""
        context_results = self.collection.get(
            where={
                "document_id": document_id,
                "chunk_index": {
                    "$gte": max(0, chunk_index - window),
                    "$lte": chunk_index + window
                }
            },
            include=["documents", "metadatas"]
        )

        # 按chunk_index排序
        sorted_chunks = sorted(
            zip(context_results["documents"], context_results["metadatas"]),
            key=lambda x: x[1]["chunk_index"]
        )

        return [{"content": doc, "metadata": meta} for doc, meta in sorted_chunks]
```

#### 11.4.4 向量数据库维护

```python
# 数据库维护和优化
class ChromaDBMaintenance:
    def __init__(self, client: chromadb.Client):
        self.client = client

    def backup_collection(self, collection_name: str, backup_path: str):
        """备份Collection"""
        collection = self.client.get_collection(collection_name)

        # 获取所有数据
        all_data = collection.get(include=["documents", "metadatas", "embeddings"])

        # 保存到文件
        backup_data = {
            "collection_name": collection_name,
            "metadata": collection.metadata,
            "data": all_data,
            "backup_time": datetime.utcnow().isoformat()
        }

        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)

    def restore_collection(self, backup_path: str):
        """恢复Collection"""
        with open(backup_path, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)

        collection_name = backup_data["collection_name"]

        # 删除现有Collection（如果存在）
        try:
            self.client.delete_collection(collection_name)
        except:
            pass

        # 创建新Collection
        collection = self.client.create_collection(
            name=collection_name,
            metadata=backup_data["metadata"]
        )

        # 恢复数据
        data = backup_data["data"]
        if data["ids"]:
            collection.add(
                ids=data["ids"],
                documents=data["documents"],
                metadatas=data["metadatas"],
                embeddings=data["embeddings"] if "embeddings" in data else None
            )

    def optimize_collection(self, collection_name: str):
        """优化Collection性能"""
        collection = self.client.get_collection(collection_name)

        # 获取统计信息
        stats = {
            "total_vectors": collection.count(),
            "collection_metadata": collection.metadata
        }

        # 检查重复向量
        duplicates = self._find_duplicate_vectors(collection)
        if duplicates:
            print(f"Found {len(duplicates)} duplicate vectors")
            # 可以选择删除重复项

        return stats

    def _find_duplicate_vectors(self, collection):
        """查找重复向量"""
        # 这里可以实现重复检测逻辑
        # 由于ChromaDB的限制，这个功能需要特殊实现
        return []

    def migrate_collection(self, old_collection: str, new_collection: str,
                          new_embedding_model: str = None):
        """迁移Collection到新的embedding模型"""
        old_coll = self.client.get_collection(old_collection)

        # 获取所有数据
        all_data = old_coll.get(include=["documents", "metadatas"])

        # 创建新Collection
        new_coll = self.client.create_collection(
            name=new_collection,
            metadata={
                **old_coll.metadata,
                "migrated_from": old_collection,
                "migration_time": datetime.utcnow().isoformat(),
                "embedding_model": new_embedding_model or old_coll.metadata.get("embedding_model")
            }
        )

        # 重新生成embeddings（如果需要）
        if new_embedding_model:
            # 这里需要重新计算embeddings
            pass
        else:
            # 直接复制数据
            new_coll.add(
                ids=all_data["ids"],
                documents=all_data["documents"],
                metadatas=all_data["metadatas"]
            )
```

### 11.5 数据库连接和事务管理

```rust
// Rust 数据库连接管理
use sqlx::{SqlitePool, Row};
use std::time::Duration;
use anyhow::Result;

pub struct DatabaseManager {
    pool: SqlitePool,
}

impl DatabaseManager {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(database_url)
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
                .busy_timeout(Duration::from_secs(30))
        ).await?;

        // 运行迁移
        sqlx::migrate!("./migrations").run(&pool).await?;

        Ok(Self { pool })
    }

    pub async fn execute_transaction<F, T>(&self, f: F) -> Result<T>
    where
        F: FnOnce(&mut sqlx::Transaction<sqlx::Sqlite>) -> Result<T> + Send,
        T: Send,
    {
        let mut tx = self.pool.begin().await?;
        let result = f(&mut tx)?;
        tx.commit().await?;
        Ok(result)
    }

    pub fn get_pool(&self) -> &SqlitePool {
        &self.pool
    }
}

// 数据库迁移管理
pub struct MigrationManager {
    pool: SqlitePool,
}

impl MigrationManager {
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    pub async fn run_migrations(&self) -> Result<()> {
        sqlx::migrate!("./migrations").run(&self.pool).await?;
        Ok(())
    }

    pub async fn get_migration_status(&self) -> Result<Vec<MigrationInfo>> {
        let rows = sqlx::query("SELECT version, description, installed_on FROM _sqlx_migrations ORDER BY version")
            .fetch_all(&self.pool)
            .await?;

        let migrations = rows.into_iter().map(|row| MigrationInfo {
            version: row.get("version"),
            description: row.get("description"),
            installed_on: row.get("installed_on"),
        }).collect();

        Ok(migrations)
    }
}

#[derive(Debug)]
pub struct MigrationInfo {
    pub version: i64,
    pub description: String,
    pub installed_on: chrono::DateTime<chrono::Utc>,
}
```

## 12. 完整REST API文档设计

### 12.1 API架构设计

AI Studio REST API 采用 RESTful 设计原则，提供统一的接口规范：

```yaml
# OpenAPI 3.0 规范
openapi: 3.0.3
info:
  title: AI Studio API
  description: AI Studio 桌面应用后端API接口文档
  version: 1.0.0
  contact:
    name: AI Studio Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器
  - url: https://api.ai-studio.com/v1
    description: 生产环境服务器

# 全局安全配置
security:
  - BearerAuth: []
  - ApiKeyAuth: []

# 安全方案定义
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  # 通用响应模式
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "Success"
        data:
          type: object
          description: 响应数据
        timestamp:
          type: string
          format: date-time
          description: 时间戳
        request_id:
          type: string
          description: 请求ID
          example: "req_123456789"

    PaginatedResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                items:
                  type: array
                  items: {}
                total:
                  type: integer
                  description: 总数量
                page:
                  type: integer
                  description: 当前页码
                limit:
                  type: integer
                  description: 每页数量
                has_more:
                  type: boolean
                  description: 是否有更多数据

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误码
        message:
          type: string
          description: 错误消息
        details:
          type: object
          description: 错误详情
        timestamp:
          type: string
          format: date-time
        request_id:
          type: string
```

### 12.2 聊天模块API详细定义

```yaml
# 聊天相关API
paths:
  /chat/sessions:
    get:
      tags:
        - Chat
      summary: 获取聊天会话列表
      description: 分页获取用户的聊天会话列表
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: search
          in: query
          description: 搜索关键词
          schema:
            type: string
        - name: sort
          in: query
          description: 排序字段
          schema:
            type: string
            enum: [created_at, updated_at, title]
            default: updated_at
        - name: order
          in: query
          description: 排序方向
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: 成功获取会话列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/ChatSession'

    post:
      tags:
        - Chat
      summary: 创建新的聊天会话
      description: 创建一个新的聊天会话
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  description: 会话标题
                  example: "新的对话"
                model_id:
                  type: string
                  description: 使用的模型ID
                  example: "llama2-7b-chat"
                system_prompt:
                  type: string
                  description: 系统提示词
                  example: "你是一个有用的AI助手"
                config:
                  $ref: '#/components/schemas/ChatConfig'
      responses:
        '201':
          description: 成功创建会话
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChatSession'

  /chat/sessions/{sessionId}:
    get:
      tags:
        - Chat
      summary: 获取指定会话详情
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功获取会话详情
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChatSession'

    put:
      tags:
        - Chat
      summary: 更新会话信息
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                system_prompt:
                  type: string
                config:
                  $ref: '#/components/schemas/ChatConfig'
      responses:
        '200':
          description: 成功更新会话
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChatSession'

    delete:
      tags:
        - Chat
      summary: 删除会话
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功删除会话
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /chat/send:
    post:
      tags:
        - Chat
      summary: 发送聊天消息
      description: 向指定会话发送消息并开始AI推理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - session_id
                - message
              properties:
                session_id:
                  type: string
                  description: 会话ID
                message:
                  type: string
                  description: 用户消息内容
                attachments:
                  type: array
                  description: 附件列表
                  items:
                    $ref: '#/components/schemas/MessageAttachment'
                model_config:
                  $ref: '#/components/schemas/ChatConfig'
      responses:
        '200':
          description: 成功发送消息
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          message_id:
                            type: string
                            description: 消息ID
                          status:
                            type: string
                            enum: [success, error]
                          stream_url:
                            type: string
                            description: 流式响应URL

  /chat/stream/{messageId}:
    get:
      tags:
        - Chat
      summary: 获取流式响应
      description: 通过Server-Sent Events获取AI响应的流式输出
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 流式响应
          content:
            text/event-stream:
              schema:
                type: string
                description: SSE事件流
              examples:
                token_event:
                  summary: Token事件
                  value: |
                    data: {"type": "token", "content": "Hello", "metadata": null}

                done_event:
                  summary: 完成事件
                  value: |
                    data: {"type": "done", "content": "", "metadata": {"tokens_used": 150, "response_time": 2.5}}

                error_event:
                  summary: 错误事件
                  value: |
                    data: {"type": "error", "content": "推理失败", "metadata": null}

  /chat/messages/{sessionId}:
    get:
      tags:
        - Chat
      summary: 获取会话消息列表
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
        - name: before
          in: query
          description: 获取指定消息之前的消息
          schema:
            type: string
        - name: after
          in: query
          description: 获取指定消息之后的消息
          schema:
            type: string
      responses:
        '200':
          description: 成功获取消息列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/ChatMessage'

# 数据模型定义
components:
  schemas:
    ChatSession:
      type: object
      properties:
        id:
          type: string
          description: 会话ID
        user_id:
          type: string
          description: 用户ID
        title:
          type: string
          description: 会话标题
        model_id:
          type: string
          description: 使用的模型ID
        system_prompt:
          type: string
          description: 系统提示词
        config:
          $ref: '#/components/schemas/ChatConfig'
        message_count:
          type: integer
          description: 消息数量
        total_tokens:
          type: integer
          description: 总token数
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间
        is_archived:
          type: boolean
          description: 是否已归档
        is_pinned:
          type: boolean
          description: 是否置顶

    ChatMessage:
      type: object
      properties:
        id:
          type: string
          description: 消息ID
        session_id:
          type: string
          description: 会话ID
        parent_id:
          type: string
          description: 父消息ID（支持消息树）
        role:
          type: string
          enum: [user, assistant, system]
          description: 消息角色
        content:
          type: string
          description: 消息内容
        content_type:
          type: string
          enum: [text, markdown, code]
          description: 内容类型
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/MessageAttachment'
        metadata:
          type: object
          description: 消息元数据
        tokens_used:
          type: integer
          description: 使用的token数
        response_time:
          type: number
          description: 响应时间（秒）
        model_used:
          type: string
          description: 使用的模型
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        is_deleted:
          type: boolean
          description: 是否已删除

    ChatConfig:
      type: object
      properties:
        model_id:
          type: string
          description: 模型ID
        temperature:
          type: number
          minimum: 0
          maximum: 2
          description: 温度参数
          default: 0.7
        max_tokens:
          type: integer
          minimum: 1
          maximum: 8192
          description: 最大token数
          default: 2048
        top_p:
          type: number
          minimum: 0
          maximum: 1
          description: Top-p参数
          default: 0.9
        frequency_penalty:
          type: number
          minimum: -2
          maximum: 2
          description: 频率惩罚
          default: 0
        presence_penalty:
          type: number
          minimum: -2
          maximum: 2
          description: 存在惩罚
          default: 0

    MessageAttachment:
      type: object
      properties:
        id:
          type: string
          description: 附件ID
        type:
          type: string
          enum: [image, document, audio, video]
          description: 附件类型
        url:
          type: string
          description: 附件URL
        name:
          type: string
          description: 附件名称
        size:
          type: integer
          description: 附件大小（字节）
        mime_type:
          type: string
          description: MIME类型
        metadata:
          type: object
          description: 附件元数据
```

### 12.3 知识库模块API详细定义

```yaml
# 知识库相关API
paths:
  /knowledge:
    get:
      tags:
        - Knowledge Base
      summary: 获取知识库列表
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: search
          in: query
          description: 搜索知识库名称
          schema:
            type: string
        - name: status
          in: query
          description: 过滤状态
          schema:
            type: string
            enum: [active, processing, error, archived]
      responses:
        '200':
          description: 成功获取知识库列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/KnowledgeBase'

    post:
      tags:
        - Knowledge Base
      summary: 创建知识库
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  description: 知识库名称
                description:
                  type: string
                  description: 知识库描述
                embedding_model:
                  type: string
                  description: 嵌入模型
                  default: "sentence-transformers/all-MiniLM-L6-v2"
                chunk_size:
                  type: integer
                  description: 分块大小
                  default: 512
                chunk_overlap:
                  type: integer
                  description: 分块重叠
                  default: 50
                config:
                  type: object
                  description: 额外配置
      responses:
        '201':
          description: 成功创建知识库
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/KnowledgeBase'

  /knowledge/{kbId}:
    get:
      tags:
        - Knowledge Base
      summary: 获取知识库详情
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功获取知识库详情
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/KnowledgeBase'

    put:
      tags:
        - Knowledge Base
      summary: 更新知识库
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                embedding_model:
                  type: string
                chunk_size:
                  type: integer
                chunk_overlap:
                  type: integer
                config:
                  type: object
      responses:
        '200':
          description: 成功更新知识库
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/KnowledgeBase'

    delete:
      tags:
        - Knowledge Base
      summary: 删除知识库
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功删除知识库
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /knowledge/{kbId}/upload:
    post:
      tags:
        - Knowledge Base
      summary: 上传文档到知识库
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 上传的文件
                auto_process:
                  type: boolean
                  description: 是否自动处理
                  default: true
                processing_config:
                  type: object
                  description: 处理配置
                  properties:
                    extract_images:
                      type: boolean
                      default: false
                    extract_tables:
                      type: boolean
                      default: true
                    ocr_enabled:
                      type: boolean
                      default: false
      responses:
        '200':
          description: 成功上传文档
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          documents:
                            type: array
                            items:
                              $ref: '#/components/schemas/Document'
                          upload_id:
                            type: string
                            description: 上传任务ID

  /knowledge/{kbId}/documents:
    get:
      tags:
        - Knowledge Base
      summary: 获取知识库文档列表
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: status
          in: query
          description: 文档状态过滤
          schema:
            type: string
            enum: [pending, processing, completed, failed, archived]
        - name: type
          in: query
          description: 文档类型过滤
          schema:
            type: string
            enum: [pdf, docx, txt, md, html]
        - name: search
          in: query
          description: 搜索文档名称
          schema:
            type: string
      responses:
        '200':
          description: 成功获取文档列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/Document'

  /knowledge/{kbId}/documents/{docId}:
    get:
      tags:
        - Knowledge Base
      summary: 获取文档详情
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
        - name: docId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功获取文档详情
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/DocumentDetail'

    delete:
      tags:
        - Knowledge Base
      summary: 删除文档
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
        - name: docId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功删除文档
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /knowledge/{kbId}/search:
    post:
      tags:
        - Knowledge Base
      summary: 语义搜索
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - query
              properties:
                query:
                  type: string
                  description: 搜索查询
                limit:
                  type: integer
                  description: 返回结果数量
                  default: 10
                  minimum: 1
                  maximum: 50
                threshold:
                  type: number
                  description: 相似度阈值
                  default: 0.7
                  minimum: 0
                  maximum: 1
                filters:
                  type: object
                  description: 搜索过滤器
                  properties:
                    document_types:
                      type: array
                      items:
                        type: string
                      description: 文档类型过滤
                    date_range:
                      type: object
                      properties:
                        start:
                          type: string
                          format: date-time
                        end:
                          type: string
                          format: date-time
                    document_ids:
                      type: array
                      items:
                        type: string
                      description: 指定文档ID
                search_type:
                  type: string
                  enum: [semantic, hybrid, keyword]
                  description: 搜索类型
                  default: semantic
                include_context:
                  type: boolean
                  description: 是否包含上下文
                  default: false
                context_window:
                  type: integer
                  description: 上下文窗口大小
                  default: 2
      responses:
        '200':
          description: 成功执行搜索
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          results:
                            type: array
                            items:
                              $ref: '#/components/schemas/SearchResult'
                          total:
                            type: integer
                            description: 总结果数
                          query_time:
                            type: number
                            description: 查询耗时（秒）
                          query_id:
                            type: string
                            description: 查询ID

# 知识库相关数据模型
components:
  schemas:
    KnowledgeBase:
      type: object
      properties:
        id:
          type: string
          description: 知识库ID
        user_id:
          type: string
          description: 用户ID
        name:
          type: string
          description: 知识库名称
        description:
          type: string
          description: 知识库描述
        embedding_model:
          type: string
          description: 嵌入模型
        chunk_size:
          type: integer
          description: 分块大小
        chunk_overlap:
          type: integer
          description: 分块重叠
        document_count:
          type: integer
          description: 文档数量
        total_chunks:
          type: integer
          description: 总块数
        total_size:
          type: integer
          description: 总大小（字节）
        status:
          type: string
          enum: [active, processing, error, archived]
          description: 状态
        config:
          type: object
          description: 配置信息
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        last_indexed_at:
          type: string
          format: date-time
          description: 最后索引时间

    Document:
      type: object
      properties:
        id:
          type: string
          description: 文档ID
        kb_id:
          type: string
          description: 知识库ID
        name:
          type: string
          description: 文档名称
        original_name:
          type: string
          description: 原始文件名
        file_type:
          type: string
          description: 文件类型
        mime_type:
          type: string
          description: MIME类型
        file_size:
          type: integer
          description: 文件大小
        file_path:
          type: string
          description: 文件路径
        content_preview:
          type: string
          description: 内容预览
        page_count:
          type: integer
          description: 页数
        word_count:
          type: integer
          description: 字数
        language:
          type: string
          description: 文档语言
        status:
          type: string
          enum: [pending, processing, completed, failed, archived]
          description: 处理状态
        processing_progress:
          type: number
          description: 处理进度
        error_message:
          type: string
          description: 错误信息
        chunks_count:
          type: integer
          description: 分块数量
        metadata:
          type: object
          description: 文档元数据
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        processed_at:
          type: string
          format: date-time

    DocumentDetail:
      allOf:
        - $ref: '#/components/schemas/Document'
        - type: object
          properties:
            chunks:
              type: array
              items:
                $ref: '#/components/schemas/DocumentChunk'
            processing_log:
              type: array
              items:
                type: object
                properties:
                  timestamp:
                    type: string
                    format: date-time
                  level:
                    type: string
                    enum: [info, warning, error]
                  message:
                    type: string

    DocumentChunk:
      type: object
      properties:
        id:
          type: string
          description: 块ID
        document_id:
          type: string
          description: 文档ID
        chunk_index:
          type: integer
          description: 块索引
        content:
          type: string
          description: 块内容
        token_count:
          type: integer
          description: Token数量
        page_number:
          type: integer
          description: 页码
        section_title:
          type: string
          description: 章节标题
        metadata:
          type: object
          description: 块元数据
        created_at:
          type: string
          format: date-time

    SearchResult:
      type: object
      properties:
        document_id:
          type: string
          description: 文档ID
        document_name:
          type: string
          description: 文档名称
        chunk_id:
          type: string
          description: 块ID
        content:
          type: string
          description: 匹配内容
        score:
          type: number
          description: 相似度分数
        metadata:
          type: object
          properties:
            page_number:
              type: integer
            chunk_index:
              type: integer
            section_title:
              type: string
            document_type:
              type: string
        context:
          type: object
          description: 上下文信息
          properties:
            before:
              type: array
              items:
                type: string
            after:
              type: array
              items:
                type: string
        highlights:
          type: array
          items:
            type: object
            properties:
              start:
                type: integer
              end:
                type: integer
              text:
                type: string
```

## 13. 性能优化方案

### 13.1 前端性能优化

#### 13.1.1 代码分割和懒加载

```typescript
// 路由级别的代码分割
const routes = [
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('@/views/Chat/index.vue'),
    meta: { title: '聊天' }
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: () => import('@/views/Knowledge/index.vue'),
    meta: { title: '知识库' }
  },
  {
    path: '/models',
    name: 'Models',
    component: () => import('@/views/Models/index.vue'),
    meta: { title: '模型管理' }
  }
]

// 组件级别的懒加载
const AsyncComponent = defineAsyncComponent({
  loader: () => import('@/components/HeavyComponent.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 动态导入优化
const loadModule = async (moduleName: string) => {
  const modules = {
    'chat': () => import('@/modules/chat'),
    'knowledge': () => import('@/modules/knowledge'),
    'models': () => import('@/modules/models')
  }

  if (modules[moduleName]) {
    return await modules[moduleName]()
  }

  throw new Error(`Module ${moduleName} not found`)
}
```

#### 13.1.2 虚拟滚动优化

```vue
<!-- 大列表虚拟滚动 -->
<template>
  <div class="virtual-list-container">
    <VirtualList
      :items="messages"
      :item-height="estimatedItemHeight"
      :container-height="containerHeight"
      :buffer-size="5"
      @scroll="handleScroll"
    >
      <template #default="{ item, index }">
        <ChatMessage
          :key="item.id"
          :message="item"
          :index="index"
          @height-change="updateItemHeight"
        />
      </template>
    </VirtualList>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import VirtualList from '@/components/common/VirtualList.vue'

const messages = ref<ChatMessage[]>([])
const containerHeight = ref(600)
const estimatedItemHeight = ref(80)
const itemHeights = ref<Map<string, number>>(new Map())

// 动态高度计算
const updateItemHeight = (itemId: string, height: number) => {
  itemHeights.value.set(itemId, height)

  // 更新平均高度估算
  const heights = Array.from(itemHeights.value.values())
  estimatedItemHeight.value = heights.reduce((sum, h) => sum + h, 0) / heights.length
}

// 滚动优化
const handleScroll = debounce((scrollTop: number) => {
  // 处理滚动事件
  console.log('Scrolled to:', scrollTop)
}, 16) // 60fps

// 内存管理
const cleanupOldItems = () => {
  if (messages.value.length > 1000) {
    // 保留最近的1000条消息
    messages.value = messages.value.slice(-1000)

    // 清理对应的高度缓存
    const keepIds = new Set(messages.value.map(m => m.id))
    for (const [id] of itemHeights.value) {
      if (!keepIds.has(id)) {
        itemHeights.value.delete(id)
      }
    }
  }
}
</script>
```

#### 13.1.3 状态管理优化

```typescript
// Pinia Store 性能优化
import { defineStore } from 'pinia'
import { ref, computed, shallowRef } from 'vue'

export const useChatStore = defineStore('chat', () => {
  // 使用 shallowRef 减少深度响应式开销
  const messages = shallowRef<ChatMessage[]>([])
  const sessions = shallowRef<ChatSession[]>([])

  // 计算属性缓存
  const messagesBySession = computed(() => {
    const map = new Map<string, ChatMessage[]>()
    for (const message of messages.value) {
      if (!map.has(message.session_id)) {
        map.set(message.session_id, [])
      }
      map.get(message.session_id)!.push(message)
    }
    return map
  })

  // 批量更新优化
  const batchUpdateMessages = (updates: Array<{id: string, changes: Partial<ChatMessage>}>) => {
    const newMessages = [...messages.value]

    for (const update of updates) {
      const index = newMessages.findIndex(m => m.id === update.id)
      if (index !== -1) {
        newMessages[index] = { ...newMessages[index], ...update.changes }
      }
    }

    messages.value = newMessages
  }

  // 内存清理
  const cleanup = () => {
    // 清理超过限制的消息
    if (messages.value.length > 10000) {
      messages.value = messages.value.slice(-5000)
    }

    // 清理无效会话
    const validSessionIds = new Set(sessions.value.map(s => s.id))
    messages.value = messages.value.filter(m => validSessionIds.has(m.session_id))
  }

  return {
    messages,
    sessions,
    messagesBySession,
    batchUpdateMessages,
    cleanup
  }
})

// 防抖和节流工具
export const usePerformanceUtils = () => {
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout

    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  }

  const throttle = <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  const requestIdleCallback = (callback: () => void) => {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(callback)
    } else {
      setTimeout(callback, 1)
    }
  }

  return {
    debounce,
    throttle,
    requestIdleCallback
  }
}
```

#### 13.1.4 资源加载优化

```typescript
// 图片懒加载
export const useLazyImage = () => {
  const imageCache = new Map<string, HTMLImageElement>()

  const loadImage = (src: string): Promise<HTMLImageElement> => {
    if (imageCache.has(src)) {
      return Promise.resolve(imageCache.get(src)!)
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        imageCache.set(src, img)
        resolve(img)
      }
      img.onerror = reject
      img.src = src
    })
  }

  const preloadImages = async (urls: string[]) => {
    const promises = urls.map(url => loadImage(url))
    await Promise.allSettled(promises)
  }

  return {
    loadImage,
    preloadImages
  }
}

// 字体预加载
const preloadFonts = () => {
  const fonts = [
    'JetBrains Mono',
    'SF Pro Display',
    'Microsoft YaHei'
  ]

  fonts.forEach(font => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'font'
    link.type = 'font/woff2'
    link.crossOrigin = 'anonymous'
    link.href = `/fonts/${font.replace(/\s+/g, '-').toLowerCase()}.woff2`
    document.head.appendChild(link)
  })
}

// 资源预取
const prefetchResources = () => {
  const resources = [
    '/api/models',
    '/api/knowledge',
    '/icons/sprite.svg'
  ]

  resources.forEach(resource => {
    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = resource
    document.head.appendChild(link)
  })
}
```

### 13.2 后端性能优化

#### 13.2.1 数据库优化

```rust
// 连接池优化
use sqlx::{SqlitePool, Row};
use std::time::Duration;

pub struct OptimizedDatabase {
    pool: SqlitePool,
    read_pool: SqlitePool,
}

impl OptimizedDatabase {
    pub async fn new(database_url: &str) -> Result<Self, sqlx::Error> {
        // 写连接池
        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(database_url)
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
                .busy_timeout(Duration::from_secs(30))
                .pragma("cache_size", "-64000") // 64MB cache
                .pragma("temp_store", "memory")
                .pragma("mmap_size", "268435456") // 256MB mmap
        ).await?;

        // 只读连接池（用于查询）
        let read_pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(database_url)
                .read_only(true)
                .busy_timeout(Duration::from_secs(10))
                .pragma("cache_size", "-32000") // 32MB cache
                .pragma("temp_store", "memory")
        ).await?;

        Ok(Self { pool, read_pool })
    }

    // 批量插入优化
    pub async fn batch_insert_messages(&self, messages: Vec<ChatMessage>) -> Result<(), sqlx::Error> {
        let mut tx = self.pool.begin().await?;

        // 使用预编译语句
        let query = sqlx::query(
            "INSERT INTO chat_messages (id, session_id, role, content, created_at) VALUES (?, ?, ?, ?, ?)"
        );

        for chunk in messages.chunks(100) {
            for message in chunk {
                query
                    .bind(&message.id)
                    .bind(&message.session_id)
                    .bind(&message.role)
                    .bind(&message.content)
                    .bind(&message.created_at)
                    .execute(&mut *tx)
                    .await?;
            }
        }

        tx.commit().await?;
        Ok(())
    }

    // 查询优化
    pub async fn get_messages_optimized(&self, session_id: &str, limit: i32, offset: i32) -> Result<Vec<ChatMessage>, sqlx::Error> {
        // 使用只读连接池
        let rows = sqlx::query(
            "SELECT id, session_id, role, content, created_at
             FROM chat_messages
             WHERE session_id = ?
             ORDER BY created_at DESC
             LIMIT ? OFFSET ?"
        )
        .bind(session_id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.read_pool)
        .await?;

        let messages = rows.into_iter().map(|row| ChatMessage {
            id: row.get("id"),
            session_id: row.get("session_id"),
            role: row.get("role"),
            content: row.get("content"),
            created_at: row.get("created_at"),
            ..Default::default()
        }).collect();

        Ok(messages)
    }
}

// 查询缓存
use moka::future::Cache;
use std::hash::{Hash, Hasher};

#[derive(Clone, Debug)]
pub struct QueryKey {
    query: String,
    params: Vec<String>,
}

impl Hash for QueryKey {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.query.hash(state);
        self.params.hash(state);
    }
}

impl PartialEq for QueryKey {
    fn eq(&self, other: &Self) -> bool {
        self.query == other.query && self.params == other.params
    }
}

impl Eq for QueryKey {}

pub struct CachedDatabase {
    db: OptimizedDatabase,
    cache: Cache<QueryKey, serde_json::Value>,
}

impl CachedDatabase {
    pub fn new(db: OptimizedDatabase) -> Self {
        let cache = Cache::builder()
            .max_capacity(1000)
            .time_to_live(Duration::from_secs(300)) // 5分钟缓存
            .time_to_idle(Duration::from_secs(60))  // 1分钟空闲过期
            .build();

        Self { db, cache }
    }

    pub async fn get_cached_query<T>(&self, key: QueryKey, query_fn: impl Future<Output = Result<T, sqlx::Error>>) -> Result<T, sqlx::Error>
    where
        T: serde::Serialize + serde::de::DeserializeOwned + Clone,
    {
        if let Some(cached) = self.cache.get(&key).await {
            if let Ok(result) = serde_json::from_value(cached) {
                return Ok(result);
            }
        }

        let result = query_fn.await?;

        if let Ok(json_value) = serde_json::to_value(&result) {
            self.cache.insert(key, json_value).await;
        }

        Ok(result)
    }
}
```

#### 13.2.2 AI推理优化

```rust
// 模型加载和推理优化
use candle_core::{Device, Tensor};
use candle_nn::VarBuilder;
use std::sync::Arc;
use tokio::sync::{RwLock, Semaphore};

pub struct OptimizedInferenceEngine {
    models: Arc<RwLock<HashMap<String, Arc<dyn Model>>>>,
    device: Device,
    inference_semaphore: Arc<Semaphore>, // 限制并发推理数量
    memory_pool: Arc<MemoryPool>,
}

impl OptimizedInferenceEngine {
    pub fn new(max_concurrent_inferences: usize) -> Result<Self, Box<dyn std::error::Error>> {
        let device = if candle_core::utils::cuda_is_available() {
            Device::new_cuda(0)?
        } else {
            Device::Cpu
        };

        Ok(Self {
            models: Arc::new(RwLock::new(HashMap::new())),
            device,
            inference_semaphore: Arc::new(Semaphore::new(max_concurrent_inferences)),
            memory_pool: Arc::new(MemoryPool::new()),
        })
    }

    // 模型预热
    pub async fn warmup_model(&self, model_id: &str) -> Result<(), Box<dyn std::error::Error>> {
        let models = self.models.read().await;
        if let Some(model) = models.get(model_id) {
            // 执行一次虚拟推理来预热模型
            let dummy_input = Tensor::zeros((1, 1), candle_core::DType::U32, &self.device)?;
            let _ = model.forward(&dummy_input).await;
        }
        Ok(())
    }

    // 批量推理
    pub async fn batch_inference(
        &self,
        model_id: &str,
        inputs: Vec<String>,
        max_batch_size: usize,
    ) -> Result<Vec<String>, Box<dyn std::error::Error>> {
        let _permit = self.inference_semaphore.acquire().await?;

        let models = self.models.read().await;
        let model = models.get(model_id)
            .ok_or("Model not found")?;

        let mut results = Vec::new();

        for batch in inputs.chunks(max_batch_size) {
            let batch_results = self.process_batch(model.clone(), batch.to_vec()).await?;
            results.extend(batch_results);
        }

        Ok(results)
    }

    async fn process_batch(
        &self,
        model: Arc<dyn Model>,
        batch: Vec<String>,
    ) -> Result<Vec<String>, Box<dyn std::error::Error>> {
        // 批量tokenization
        let tokenized = self.batch_tokenize(&batch).await?;

        // 批量推理
        let outputs = model.batch_forward(tokenized).await?;

        // 批量解码
        let decoded = self.batch_decode(outputs).await?;

        Ok(decoded)
    }

    // 内存管理
    async fn cleanup_memory(&self) {
        self.memory_pool.cleanup().await;

        // 强制垃圾回收（如果需要）
        if self.memory_pool.usage_ratio() > 0.8 {
            self.force_gc().await;
        }
    }

    async fn force_gc(&self) {
        // 实现内存清理逻辑
        // 可能包括卸载不常用的模型、清理缓存等
    }
}

// 内存池管理
pub struct MemoryPool {
    allocated: Arc<RwLock<usize>>,
    max_memory: usize,
    buffers: Arc<RwLock<Vec<Vec<u8>>>>,
}

impl MemoryPool {
    pub fn new() -> Self {
        Self {
            allocated: Arc::new(RwLock::new(0)),
            max_memory: 8 * 1024 * 1024 * 1024, // 8GB
            buffers: Arc::new(RwLock::new(Vec::new())),
        }
    }

    pub async fn allocate(&self, size: usize) -> Option<Vec<u8>> {
        let mut allocated = self.allocated.write().await;

        if *allocated + size > self.max_memory {
            return None;
        }

        *allocated += size;
        Some(vec![0; size])
    }

    pub async fn deallocate(&self, buffer: Vec<u8>) {
        let size = buffer.len();
        let mut allocated = self.allocated.write().await;
        *allocated = allocated.saturating_sub(size);

        // 可以选择回收buffer到池中
        let mut buffers = self.buffers.write().await;
        if buffers.len() < 100 { // 限制池大小
            buffers.push(buffer);
        }
    }

    pub async fn usage_ratio(&self) -> f64 {
        let allocated = *self.allocated.read().await;
        allocated as f64 / self.max_memory as f64
    }

    pub async fn cleanup(&self) {
        let mut buffers = self.buffers.write().await;
        buffers.clear();
    }
}
```

## 14. 项目总结

### 10.1 技术亮点

**创新技术应用**
- **本地AI推理**：集成Candle和llama.cpp双引擎，支持多种模型格式
- **向量数据库**：ChromaDB实现高效语义搜索和RAG增强
- **跨平台架构**：Tauri框架实现真正的跨平台桌面应用
- **多模态处理**：集成OCR、TTS、ASR等多种AI能力
- **P2P网络**：mDNS设备发现和安全的点对点通信
- **插件系统**：WASM沙箱环境，安全的第三方扩展

**性能优化特色**
- **内存管理**：智能模型加载和卸载，优化内存使用
- **并发处理**：基于Tokio的异步架构，支持高并发
- **缓存策略**：多层缓存机制，提升响应速度
- **流式处理**：实时流式输出，提升用户体验
- **批量优化**：批量处理和数据库操作优化

### 10.2 核心优势

**技术优势**
- **完全离线**：无需依赖云端服务，保护数据隐私
- **高性能**：Rust后端 + Vue3前端，性能卓越
- **可扩展**：模块化设计，支持功能扩展
- **安全可靠**：多层安全机制，数据加密存储
- **用户友好**：现代化UI设计，操作简单直观

**业务优势**
- **企业级**：满足企业对数据安全和性能的要求
- **成本效益**：一次部署，长期使用，无持续费用
- **定制化**：支持插件扩展，满足特定需求
- **协作性**：局域网共享，团队协作便利
- **多样性**：支持多种AI模型和多模态处理

### 10.3 应用场景

**企业应用**
- **内部知识管理**：企业文档智能检索和问答
- **代码辅助**：程序员代码生成和技术咨询
- **文档处理**：批量文档分析和内容提取
- **培训教育**：企业培训材料智能问答

**个人应用**
- **学习助手**：个人知识库管理和学习辅导
- **写作助手**：文章写作和内容创作支持
- **研究工具**：学术研究资料整理和分析
- **生活助手**：日常问题咨询和信息查询

**专业应用**
- **法律咨询**：法律文档检索和案例分析
- **医疗辅助**：医学文献查询和诊断辅助
- **金融分析**：财务报告分析和投资建议
- **技术支持**：技术文档查询和问题解决

### 10.4 发展规划

**短期目标 (3-6个月)**
- 完成核心功能开发和测试
- 发布第一个稳定版本
- 建立用户社区和反馈机制
- 优化性能和用户体验

**中期目标 (6-12个月)**
- 扩展支持的模型类型和格式
- 增强多模态处理能力
- 完善插件生态系统
- 支持更多操作系统平台

**长期目标 (1-2年)**
- 构建完整的AI工具生态
- 支持分布式部署和集群
- 集成更多AI能力和服务
- 建立商业化运营模式

### 10.5 风险评估

**技术风险**
- **模型兼容性**：新模型格式的适配挑战
- **性能瓶颈**：大模型推理的性能限制
- **安全漏洞**：插件系统的安全风险
- **平台差异**：跨平台兼容性问题

**市场风险**
- **竞争激烈**：AI工具市场竞争加剧
- **技术变化**：AI技术快速发展和变化
- **用户接受度**：用户对本地AI工具的接受程度
- **法规变化**：AI相关法规政策的变化

**应对策略**
- **持续创新**：保持技术领先和功能创新
- **社区建设**：建立活跃的开发者和用户社区
- **质量保证**：严格的测试和质量控制流程
- **风险监控**：建立风险监控和应急响应机制

---

**文档版本**：v1.0
**最后更新**：2024年12月
**文档状态**：完整版
**维护团队**：AI Studio开发团队

> 本文档详细描述了AI Studio项目的完整技术方案，包括架构设计、功能模块、接口定义、部署运维等各个方面。文档将随着项目开发进展持续更新和完善。

### 7.5 多模态处理模块API接口

**OCR识别接口**
```typescript
POST /api/multimodal/ocr
Request: FormData {
  image: File;
  language?: string;
  output_format?: 'text' | 'json' | 'pdf';
}
Response: {
  task_id: string;
  text: string;
  confidence: number;
  bounding_boxes?: Array<{
    text: string;
    x: number;
    y: number;
    width: number;
    height: number;
    confidence: number;
  }>;
  processing_time: number;
}
```

**语音处理接口**
```typescript
// 文字转语音
POST /api/multimodal/tts
Request: {
  text: string;
  voice?: string;
  speed?: number;
  pitch?: number;
  output_format?: 'wav' | 'mp3' | 'ogg';
}
Response: {
  task_id: string;
  audio_url: string;
  duration: number;
  file_size: number;
}

// 语音转文字
POST /api/multimodal/asr
Request: FormData {
  audio: File;
  language?: string;
  model?: string;
}
Response: {
  task_id: string;
  text: string;
  confidence: number;
  segments?: Array<{
    text: string;
    start: number;
    end: number;
    confidence: number;
  }>;
  processing_time: number;
}
```

**图像和视频处理接口**
```typescript
// 图像分析
POST /api/multimodal/image/analyze
Request: FormData {
  image: File;
  analysis_type: 'description' | 'objects' | 'text' | 'faces';
}

// 视频分析
POST /api/multimodal/video/analyze
Request: FormData {
  video: File;
  analysis_type: 'summary' | 'subtitles' | 'objects';
}

// 格式转换
POST /api/multimodal/convert
Request: FormData {
  file: File;
  target_format: string;
  quality?: number;
}
```

**任务管理接口**
```typescript
// 获取任务列表
GET /api/multimodal/tasks
Query: {
  status?: string;
  type?: string;
  page?: number;
  limit?: number;
}

// 获取任务详情
GET /api/multimodal/tasks/{id}

// 删除任务
DELETE /api/multimodal/tasks/{id}

// 获取处理历史
GET /api/multimodal/history
Query: {
  page?: number;
  limit?: number;
  date_range?: {
    start: string;
    end: string;
  };
}
```

### 7.6 远程配置模块API接口

**配置管理接口**
```typescript
// 获取配置列表
GET /api/remote/configs
Response: {
  configs: Array<{
    id: string;
    name: string;
    provider: string;
    model_name: string;
    is_active: boolean;
    last_test_at?: string;
    status: 'connected' | 'disconnected' | 'error';
    created_at: string;
  }>;
}

// 创建配置
POST /api/remote/configs
Request: {
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'custom';
  api_key: string;
  base_url?: string;
  model_name: string;
  max_tokens?: number;
  temperature?: number;
  proxy?: {
    type: 'http' | 'https' | 'socks5';
    host: string;
    port: number;
    username?: string;
    password?: string;
  };
}

// 更新配置
PUT /api/remote/configs/{id}
Request: {
  name?: string;
  api_key?: string;
  base_url?: string;
  model_name?: string;
  max_tokens?: number;
  temperature?: number;
  proxy?: object;
}

// 删除配置
DELETE /api/remote/configs/{id}

// 测试配置
POST /api/remote/configs/{id}/test
Response: {
  status: 'success' | 'error';
  response_time?: number;
  error_message?: string;
  model_info?: {
    name: string;
    context_length: number;
    capabilities: string[];
  };
}

// 激活配置
POST /api/remote/configs/{id}/activate
```

## 7. API接口设计

### 7.1 接口设计规范

**RESTful API设计原则**
- **统一资源标识符**：使用清晰的URL路径结构
- **HTTP方法语义**：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- **状态码规范**：200(成功)、201(创建)、400(请求错误)、401(未授权)、404(未找到)、500(服务器错误)
- **响应格式统一**：JSON格式，包含data、message、code字段

**接口响应格式**
```typescript
interface ApiResponse<T> {
  code: number;           // 状态码
  message: string;        // 响应消息
  data?: T;              // 响应数据
  timestamp: number;      // 时间戳
  request_id: string;     // 请求ID
}
```

**分页响应格式**
```typescript
interface PaginatedResponse<T> {
  items: T[];            // 数据列表
  total: number;         // 总数量
  page: number;          // 当前页码
  limit: number;         // 每页数量
  has_more: boolean;     // 是否有更多数据
}
```

### 7.2 聊天模块API接口

**发送消息接口**
```typescript
POST /api/chat/send
Request: {
  session_id: string;
  message: string;
  attachments?: Array<{
    type: 'image' | 'document' | 'audio';
    url: string;
    name: string;
    size: number;
  }>;
  model_config?: {
    model_id: string;
    temperature: number;
    max_tokens: number;
    top_p: number;
  };
}
Response: {
  message_id: string;
  status: 'success' | 'error';
  error?: string;
}
```

**SSE流式响应接口**
```typescript
GET /api/chat/stream/{message_id}
Response: Server-Sent Events
data: {
  type: 'token' | 'done' | 'error';
  content: string;
  metadata?: {
    tokens_used: number;
    response_time: number;
  };
}
```

**会话管理接口**
```typescript
// 获取会话列表
GET /api/chat/sessions
Query: {
  page?: number;
  limit?: number;
  search?: string;
  sort?: 'created_at' | 'updated_at' | 'title';
  order?: 'asc' | 'desc';
}

// 创建会话
POST /api/chat/sessions
Request: {
  title?: string;
  model_id?: string;
  system_prompt?: string;
  config?: {
    temperature: number;
    max_tokens: number;
    top_p: number;
  };
}

// 更新会话
PUT /api/chat/sessions/{id}
Request: {
  title?: string;
  system_prompt?: string;
  config?: object;
}

// 删除会话
DELETE /api/chat/sessions/{id}

// 获取会话消息
GET /api/chat/messages/{session_id}
Query: {
  page?: number;
  limit?: number;
  before?: string;
  after?: string;
}
```

### 7.3 知识库模块API接口

**知识库管理接口**
```typescript
// 创建知识库
POST /api/knowledge
Request: {
  name: string;
  description?: string;
  embedding_model?: string;
  chunk_size?: number;
  chunk_overlap?: number;
}

// 获取知识库列表
GET /api/knowledge
Query: {
  page?: number;
  limit?: number;
  search?: string;
}

// 更新知识库
PUT /api/knowledge/{id}
Request: {
  name?: string;
  description?: string;
  embedding_model?: string;
  chunk_size?: number;
  chunk_overlap?: number;
}

// 删除知识库
DELETE /api/knowledge/{id}
```

**文档管理接口**
```typescript
// 上传文档
POST /api/knowledge/{kb_id}/upload
Request: FormData {
  files: File[];
  auto_process?: boolean;
}
Response: {
  documents: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    status: 'processing' | 'completed' | 'failed';
  }>;
}

// 获取文档列表
GET /api/knowledge/{kb_id}/documents
Query: {
  page?: number;
  limit?: number;
  status?: string;
  type?: string;
}

// 删除文档
DELETE /api/knowledge/{kb_id}/documents/{doc_id}
```

**语义搜索接口**
```typescript
POST /api/knowledge/{kb_id}/search
Request: {
  query: string;
  limit?: number;
  threshold?: number;
  filters?: {
    document_types?: string[];
    date_range?: {
      start: string;
      end: string;
    };
  };
}
Response: {
  results: Array<{
    document_id: string;
    document_name: string;
    chunk_id: string;
    content: string;
    score: number;
    metadata: {
      page_number?: number;
      chunk_index: number;
    };
  }>;
  total: number;
  query_time: number;
}
```
